#!/usr/bin/env python3
"""
Database Migration Script for Preferred Barber Feature
This script adds the required columns and tables for the preferred barber functionality
"""

import sqlite3
import os
import sys
from datetime import datetime

def migrate_database():
    """Migrate the existing database to add preferred barber functionality"""

    # Try different possible database paths
    possible_paths = [
        'salon_management.db',
        'instance/salon_management.db',
        os.path.join('instance', 'salon_management.db')
    ]

    db_path = None
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break

    if not db_path:
        print("❌ Database file not found!")
        print("   Searched in:")
        for path in possible_paths:
            print(f"   - {path}")
        return False

    print(f"📁 Found database at: {db_path}")
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Starting database migration for Preferred Barber feature...")
        
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(customers)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # Add preferred_barber_id column if it doesn't exist
        if 'preferred_barber_id' not in columns:
            print("   ➕ Adding preferred_barber_id column to customers table...")
            cursor.execute("""
                ALTER TABLE customers 
                ADD COLUMN preferred_barber_id INTEGER 
                REFERENCES barbers(id)
            """)
            print("   ✅ preferred_barber_id column added successfully")
        else:
            print("   ℹ️  preferred_barber_id column already exists")
        
        # Add barber_loyalty_points column if it doesn't exist
        if 'barber_loyalty_points' not in columns:
            print("   ➕ Adding barber_loyalty_points column to customers table...")
            cursor.execute("""
                ALTER TABLE customers 
                ADD COLUMN barber_loyalty_points INTEGER DEFAULT 0
            """)
            print("   ✅ barber_loyalty_points column added successfully")
        else:
            print("   ℹ️  barber_loyalty_points column already exists")
        
        # Check if customer_barber_history table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='customer_barber_history'
        """)
        
        if not cursor.fetchone():
            print("   ➕ Creating customer_barber_history table...")
            cursor.execute("""
                CREATE TABLE customer_barber_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER NOT NULL,
                    barber_id INTEGER NOT NULL,
                    visit_date DATETIME NOT NULL,
                    service_rating FLOAT DEFAULT 0.0,
                    service_notes TEXT,
                    total_amount FLOAT DEFAULT 0.0,
                    services_provided TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (barber_id) REFERENCES barbers (id)
                )
            """)
            print("   ✅ customer_barber_history table created successfully")
        else:
            print("   ℹ️  customer_barber_history table already exists")
        
        # Commit the changes
        conn.commit()
        
        # Add some sample data if tables are empty
        add_sample_data(cursor, conn)
        
        # Verify the migration
        verify_migration(cursor)
        
        conn.close()
        
        print("🎉 Database migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def add_sample_data(cursor, conn):
    """Add sample data for testing"""
    
    try:
        # Check if we have customers and barbers
        cursor.execute("SELECT COUNT(*) FROM customers")
        customer_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM barbers")
        barber_count = cursor.fetchone()[0]
        
        if customer_count == 0 or barber_count == 0:
            print("   ℹ️  No existing customers or barbers found. Skipping sample data.")
            return
        
        # Check if we already have preferred barber relationships
        cursor.execute("SELECT COUNT(*) FROM customers WHERE preferred_barber_id IS NOT NULL")
        existing_relationships = cursor.fetchone()[0]
        
        if existing_relationships > 0:
            print("   ℹ️  Preferred barber relationships already exist. Skipping sample data.")
            return
        
        print("   📊 Adding sample preferred barber relationships...")
        
        # Get first few customers and barbers
        cursor.execute("SELECT id FROM customers LIMIT 3")
        customers = cursor.fetchall()
        
        cursor.execute("SELECT id FROM barbers WHERE is_active = 1 LIMIT 2")
        barbers = cursor.fetchall()
        
        if not customers or not barbers:
            print("   ⚠️  Not enough customers or barbers for sample data")
            return
        
        # Assign preferred barbers to customers
        for i, (customer_id,) in enumerate(customers):
            barber_id = barbers[i % len(barbers)][0]
            loyalty_points = (i + 1) * 15  # 15, 30, 45 points
            
            # Update customer with preferred barber
            cursor.execute("""
                UPDATE customers 
                SET preferred_barber_id = ?, barber_loyalty_points = ?
                WHERE id = ?
            """, (barber_id, loyalty_points, customer_id))
            
            # Add history record
            cursor.execute("""
                INSERT INTO customer_barber_history 
                (customer_id, barber_id, visit_date, service_rating, service_notes, total_amount, services_provided)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                customer_id,
                barber_id,
                datetime.now(),
                4.5 + (i * 0.2),  # Ratings between 4.5-5.0
                f"عميل مفضل - تم تعيين الحلاق المفضل",
                50.0 + (i * 10),  # Amounts 50, 60, 70
                "حلاقة عادية، تهذيب ذقن"
            ))
            
            print(f"   ✓ Customer {customer_id} assigned to barber {barber_id} with {loyalty_points} loyalty points")
        
        conn.commit()
        print("   ✅ Sample data added successfully")
        
    except Exception as e:
        print(f"   ⚠️  Error adding sample data: {e}")

def verify_migration(cursor):
    """Verify that the migration was successful"""
    
    print("🔍 Verifying migration...")
    
    try:
        # Check customers table structure
        cursor.execute("PRAGMA table_info(customers)")
        columns = [column[1] for column in cursor.fetchall()]
        
        required_columns = ['preferred_barber_id', 'barber_loyalty_points']
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"   ❌ Missing columns in customers table: {missing_columns}")
            return False
        
        print("   ✅ All required columns exist in customers table")
        
        # Check customer_barber_history table
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='customer_barber_history'
        """)
        
        if not cursor.fetchone():
            print("   ❌ customer_barber_history table not found")
            return False
        
        print("   ✅ customer_barber_history table exists")
        
        # Check data
        cursor.execute("SELECT COUNT(*) FROM customers WHERE preferred_barber_id IS NOT NULL")
        customers_with_preferred = cursor.fetchone()[0]
        
        cursor.execute("SELECT SUM(barber_loyalty_points) FROM customers WHERE barber_loyalty_points > 0")
        total_loyalty_points = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT COUNT(*) FROM customer_barber_history")
        history_records = cursor.fetchone()[0]
        
        print(f"   📊 Migration Statistics:")
        print(f"      • Customers with preferred barber: {customers_with_preferred}")
        print(f"      • Total loyalty points: {total_loyalty_points}")
        print(f"      • History records: {history_records}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error during verification: {e}")
        return False

def main():
    """Main function"""
    
    print("=" * 60)
    print("🇶🇦 Qatar Men's Salon - Database Migration")
    print("   Adding Preferred Barber Feature")
    print("=" * 60)
    
    if migrate_database():
        print("\n✅ Migration completed successfully!")
        print("\nYou can now:")
        print("   • Assign preferred barbers to customers")
        print("   • Track loyalty points")
        print("   • View customer-barber relationship history")
        print("   • Generate preferred barber reports")
        print("\n🚀 Restart your application to use the new features!")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above and try again.")
        return 1
    
    print("\n" + "=" * 60)
    return 0

if __name__ == "__main__":
    exit(main())
