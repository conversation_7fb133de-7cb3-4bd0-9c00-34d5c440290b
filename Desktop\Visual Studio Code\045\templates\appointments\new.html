{% extends "base.html" %}

{% block title %}موعد جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="fas fa-calendar-plus me-2"></i>
                    حجز موعد جديد
                </h1>
                <a href="{{ url_for('appointments') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمواعيد
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>
                        تفاصيل الموعد
                    </h5>
                </div>
                <div class="card-body">
                    <form id="appointmentForm">
                        <!-- Customer Selection -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customerId" class="form-label">العميل *</label>
                                    <select class="form-select" id="customerId" required>
                                        <option value="">اختر العميل...</option>
                                        {% for customer in customers %}
                                        <option value="{{ customer.id }}" 
                                                {% if selected_customer and selected_customer.id == customer.id %}selected{% endif %}
                                                data-phone="{{ customer.phone }}"
                                                data-preferred-barber="{{ customer.preferred_barber_id or '' }}">
                                            {{ customer.name }} - {{ customer.phone }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">
                                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="showNewCustomerModal()">
                                            <i class="fas fa-plus me-1"></i>
                                            إضافة عميل جديد
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="barberId" class="form-label">الحلاق *</label>
                                    <select class="form-select" id="barberId" required onchange="updateAvailableChairs()">
                                        <option value="">اختر الحلاق...</option>
                                        {% for barber in barbers %}
                                        <option value="{{ barber.id }}"
                                                data-specialties="{{ barber.specialties or '' }}"
                                                data-rating="{{ barber.rating or 0 }}">
                                            {{ barber.name }}
                                            {% if barber.specialties %}
                                            - {{ barber.specialties }}
                                            {% endif %}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Date and Time -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="appointmentDate" class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" id="appointmentDate"
                                           required onchange="updateAvailableSlots()">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="appointmentTime" class="form-label">الوقت *</label>
                                    <select class="form-select" id="appointmentTime" required>
                                        <option value="">اختر الوقت...</option>
                                    </select>
                                    <div class="form-text">سيتم تحديث الأوقات المتاحة بناءً على التاريخ والحلاق المختار</div>
                                </div>
                            </div>
                        </div>

                        <!-- Chair Selection -->
                        <div class="mb-3">
                            <label class="form-label">اختيار الكرسي *</label>
                            <div id="chairSelection" class="row">
                                <!-- Chairs will be loaded dynamically -->
                            </div>
                            <input type="hidden" id="selectedChairId" required>
                            <div class="form-text">انقر على الكرسي لاختياره. الكراسي المتاحة ستظهر باللون الأخضر</div>
                        </div>

                        <!-- Services -->
                        <div class="mb-3">
                            <label class="form-label">الخدمات *</label>
                            <div class="row" id="servicesSelection">
                                {% for service in services %}
                                <div class="col-md-6 col-lg-4 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               id="service{{ service.id }}" 
                                               value="{{ service.id }}"
                                               data-price="{{ service.price }}"
                                               data-duration="{{ service.duration_minutes }}"
                                               onchange="updateTotalAmount()">
                                        <label class="form-check-label" for="service{{ service.id }}">
                                            {{ service.name }}
                                            <span class="text-muted">({{ service.price }} ر.ق)</span>
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Total Amount -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="totalAmount" class="form-label">المبلغ الإجمالي</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="totalAmount" 
                                               step="0.01" min="0" readonly>
                                        <span class="input-group-text">ر.ق</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="estimatedDuration" class="form-label">المدة المتوقعة</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="estimatedDuration" readonly>
                                        <span class="input-group-text">دقيقة</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-3">
                            <label for="appointmentNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="appointmentNotes" rows="3" 
                                      placeholder="أي ملاحظات خاصة بالموعد..."></textarea>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-secondary me-md-2" onclick="window.history.back()">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </button>
                            <button type="button" class="btn btn-primary" onclick="saveAppointment()">
                                <i class="fas fa-save me-2"></i>
                                حفظ الموعد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Chair Status Legend -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        دليل حالة الكراسي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <div class="chair-legend available me-2"></div>
                        <span>متاح للحجز</span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <div class="chair-legend occupied me-2"></div>
                        <span>مشغول</span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <div class="chair-legend maintenance me-2"></div>
                        <span>في الصيانة</span>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <div class="chair-legend selected me-2"></div>
                        <span>محدد للحجز</span>
                    </div>
                </div>
            </div>

            <!-- Selected Customer Info -->
            <div class="card mb-4" id="customerInfoCard" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        معلومات العميل
                    </h5>
                </div>
                <div class="card-body" id="customerInfo">
                    <!-- Customer info will be loaded here -->
                </div>
            </div>

            <!-- Selected Chair Info -->
            <div class="card mb-4" id="chairInfoCard" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chair me-2"></i>
                        معلومات الكرسي
                    </h5>
                </div>
                <div class="card-body" id="chairInfo">
                    <!-- Chair info will be loaded here -->
                </div>
            </div>

            <!-- Appointment Summary -->
            <div class="card" id="appointmentSummary" style="display: none;">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clipboard-list me-2"></i>
                        ملخص الموعد
                    </h5>
                </div>
                <div class="card-body" id="summaryContent">
                    <!-- Summary will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Customer Modal -->
<div class="modal fade" id="newCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عميل جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="newCustomerForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="newCustomerName" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="newCustomerName" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="newCustomerPhone" class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="newCustomerPhone" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="newCustomerEmail" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="newCustomerEmail">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveNewCustomerQuick()">
                    <i class="fas fa-save me-2"></i>
                    حفظ العميل
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let selectedChair = null;
let availableChairs = [];
let chairsData = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded, initializing...');

    // Set default date to today and minimum date
    const today = new Date().toISOString().split('T')[0];
    const appointmentDateInput = document.getElementById('appointmentDate');
    appointmentDateInput.value = today;
    appointmentDateInput.min = today;

    // Set up event listeners
    setupEventListeners();

    // Load chairs data
    console.log('Loading chairs data...');
    loadChairsData();

    // Pre-select customer if provided
    {% if selected_customer %}
    console.log('Pre-selecting customer: {{ selected_customer.id }}');
    selectCustomer({{ selected_customer.id }});
    {% endif %}

    // Pre-select chair if provided
    {% if selected_chair %}
    console.log('Pre-selecting chair: {{ selected_chair.id }}');
    selectChair({{ selected_chair.id }});
    {% endif %}
});

function setupEventListeners() {
    // Customer selection change
    document.getElementById('customerId').addEventListener('change', function() {
        const customerId = this.value;
        if (customerId) {
            selectCustomer(customerId);

            // Auto-select preferred barber if available
            const selectedOption = this.options[this.selectedIndex];
            const preferredBarberId = selectedOption.dataset.preferredBarber;
            if (preferredBarberId) {
                document.getElementById('barberId').value = preferredBarberId;
                updateAvailableChairs();
            }
        } else {
            hideCustomerInfo();
        }
    });

    // Date change
    document.getElementById('appointmentDate').addEventListener('change', function() {
        updateAvailableSlots();
        updateAvailableChairs();
    });

    // Barber change
    document.getElementById('barberId').addEventListener('change', function() {
        updateAvailableChairs();
        updateAvailableSlots();
    });
}

function loadChairsData() {
    console.log('Loading chairs data...');
    fetch('/api/chairs/status')
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Chairs data received:', data);
            chairsData = data;
            renderChairs();
        })
        .catch(error => {
            console.error('Error loading chairs:', error);
            showError('حدث خطأ في تحميل بيانات الكراسي: ' + error.message);
        });
}

function renderChairs() {
    console.log('Rendering chairs...', chairsData);
    const container = document.getElementById('chairSelection');

    if (!container) {
        console.error('Chair selection container not found!');
        return;
    }

    container.innerHTML = '';

    if (!chairsData || chairsData.length === 0) {
        container.innerHTML = '<div class="col-12"><p class="text-muted text-center">لا توجد كراسي متاحة</p></div>';
        return;
    }

    chairsData.forEach(chair => {
        const chairElement = document.createElement('div');
        chairElement.className = 'col-auto mb-2';

        const statusClass = getChairStatusClass(chair);
        const isClickable = chair.status === 'available';

        chairElement.innerHTML = `
            <div class="chair-item ${statusClass}"
                 data-chair-id="${chair.id}"
                 ${isClickable ? `onclick="selectChair(${chair.id})"` : ''}
                 data-bs-toggle="tooltip"
                 title="كرسي ${chair.number} - ${getStatusText(chair.status)}">
                <div class="chair-number">${chair.number}</div>
                <div class="chair-status">${getStatusText(chair.status)}</div>
            </div>
        `;

        container.appendChild(chairElement);
    });

    console.log('Chairs rendered successfully');

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function getChairStatusClass(chair) {
    const statusMap = {
        'available': 'available',
        'occupied': 'occupied',
        'maintenance': 'maintenance',
        'vacation': 'maintenance'
    };
    return statusMap[chair.status] || 'maintenance';
}

function getStatusText(status) {
    const statusMap = {
        'available': 'متاح',
        'occupied': 'مشغول',
        'maintenance': 'صيانة',
        'vacation': 'إجازة'
    };
    return statusMap[status] || 'غير متاح';
}

function selectChair(chairId) {
    const chair = chairsData.find(c => c.id === chairId);
    if (!chair || chair.status !== 'available') {
        return;
    }

    // Remove previous selection
    document.querySelectorAll('.chair-item').forEach(item => {
        item.classList.remove('selected');
    });

    // Add selection to clicked chair
    const chairElement = document.querySelector(`[data-chair-id="${chairId}"]`);
    chairElement.classList.add('selected');

    // Store selection
    selectedChair = chair;
    document.getElementById('selectedChairId').value = chairId;

    // Show chair info
    showChairInfo(chair);

    // Update summary
    updateAppointmentSummary();

    // Show success notification
    showInfoNotification(`تم اختيار الكرسي رقم ${chair.number}`);
}

function selectCustomer(customerId) {
    // Show loading state
    const card = document.getElementById('customerInfoCard');
    const content = document.getElementById('customerInfo');

    card.style.display = 'block';
    content.innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-2 mb-0 text-muted">جاري تحميل معلومات العميل...</p>
        </div>
    `;

    fetch(`/api/customers/${customerId}`)
        .then(response => response.json())
        .then(customer => {
            showCustomerInfo(customer);

            // Auto-select preferred barber if available
            if (customer.preferred_barber_id) {
                const barberSelect = document.getElementById('barberId');
                const preferredOption = barberSelect.querySelector(`option[value="${customer.preferred_barber_id}"]`);
                if (preferredOption) {
                    barberSelect.value = customer.preferred_barber_id;
                    updateAvailableChairs();
                    updateAvailableSlots();
                    showInfoNotification(`تم اختيار الحلاق المفضل: ${customer.preferred_barber_name}`);
                } else {
                    showInfoNotification(`تم تحميل معلومات العميل: ${customer.name}`);
                }
            } else {
                showInfoNotification(`تم تحميل معلومات العميل: ${customer.name}`);
            }

            updateAppointmentSummary();
        })
        .catch(error => {
            console.error('Error loading customer:', error);
            showError('حدث خطأ في تحميل بيانات العميل');
            hideCustomerInfo();
        });
}

function showCustomerInfo(customer) {
    const card = document.getElementById('customerInfoCard');
    const content = document.getElementById('customerInfo');

    content.innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <h6 class="mb-2 text-primary">
                    <i class="fas fa-user me-2"></i>${customer.name}
                    ${customer.is_vip ? '<span class="badge bg-warning text-dark ms-2">VIP</span>' : ''}
                </h6>
                <p class="mb-1"><i class="fas fa-phone me-2 text-success"></i>${customer.phone}</p>
                ${customer.email ? `<p class="mb-1"><i class="fas fa-envelope me-2 text-info"></i>${customer.email}</p>` : ''}
                ${customer.preferred_barber_name ? `<p class="mb-1"><i class="fas fa-star me-2 text-warning"></i>الحلاق المفضل: ${customer.preferred_barber_name}</p>` : ''}
                ${customer.last_visit ? `<p class="mb-1"><i class="fas fa-calendar me-2 text-secondary"></i>آخر زيارة: ${customer.last_visit}</p>` : ''}
            </div>
            <div class="col-md-4">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="bg-light p-2 rounded">
                            <small class="text-muted d-block">الزيارات</small>
                            <div class="fw-bold text-primary">${customer.visit_count}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-light p-2 rounded">
                            <small class="text-muted d-block">الإنفاق</small>
                            <div class="fw-bold text-success">${customer.total_spent} ر.ق</div>
                        </div>
                    </div>
                </div>
                ${customer.barber_loyalty_points > 0 ? `
                    <div class="mt-2 text-center">
                        <div class="bg-warning bg-opacity-25 p-2 rounded">
                            <small class="text-muted d-block">نقاط الولاء</small>
                            <div class="fw-bold text-warning">${customer.barber_loyalty_points}</div>
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
        ${customer.notes ? `
            <div class="mt-2">
                <small class="text-muted">ملاحظات:</small>
                <div class="bg-light p-2 rounded mt-1">
                    <small>${customer.notes}</small>
                </div>
            </div>
        ` : ''}
        <div class="mt-3 pt-2 border-top">
            <div class="row g-2">
                <div class="col-6">
                    <button type="button" class="btn btn-outline-primary btn-sm w-100" onclick="viewCustomerHistory(${customer.id})">
                        <i class="fas fa-history me-1"></i>التاريخ
                    </button>
                </div>
                <div class="col-6">
                    <button type="button" class="btn btn-outline-info btn-sm w-100" onclick="editCustomer(${customer.id})">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </button>
                </div>
            </div>
        </div>
    `;

    card.style.display = 'block';

    // Add animation
    card.style.opacity = '0';
    card.style.transform = 'translateY(-10px)';
    setTimeout(() => {
        card.style.transition = 'all 0.3s ease';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
    }, 10);
}

function hideCustomerInfo() {
    document.getElementById('customerInfoCard').style.display = 'none';
}

function showChairInfo(chair) {
    const card = document.getElementById('chairInfoCard');
    const content = document.getElementById('chairInfo');

    content.innerHTML = `
        <div class="text-center">
            <h6 class="mb-3 text-primary">
                <i class="fas fa-chair me-2"></i>كرسي رقم ${chair.number}
                ${chair.name && chair.name !== `كرسي رقم ${chair.number}` ? `<br><small class="text-muted">${chair.name}</small>` : ''}
            </h6>
            <div class="mb-3">
                <span class="badge bg-success fs-6 px-3 py-2">
                    <i class="fas fa-check-circle me-1"></i>متاح للحجز
                </span>
            </div>
            ${chair.barber ? `
                <div class="bg-light p-3 rounded">
                    <p class="mb-0">
                        <i class="fas fa-user me-2 text-primary"></i>
                        <strong>الحلاق المخصص:</strong> ${chair.barber}
                    </p>
                </div>
            ` : `
                <div class="bg-light p-3 rounded">
                    <p class="mb-0 text-muted">
                        <i class="fas fa-users me-2"></i>
                        متاح لجميع الحلاقين
                    </p>
                </div>
            `}
        </div>
    `;

    card.style.display = 'block';

    // Add animation
    card.style.opacity = '0';
    card.style.transform = 'translateY(-10px)';
    setTimeout(() => {
        card.style.transition = 'all 0.3s ease';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
    }, 10);
}

function updateAvailableChairs() {
    const barberId = document.getElementById('barberId').value;
    const appointmentDate = document.getElementById('appointmentDate').value;
    const appointmentTime = document.getElementById('appointmentTime').value;

    if (!barberId) {
        // Show all available chairs if no barber selected
        loadChairsData();
        return;
    }

    // Build query parameters
    let url = `/api/chairs/available?barber_id=${barberId}`;
    if (appointmentDate) {
        url += `&date=${appointmentDate}`;
    }
    if (appointmentTime) {
        url += `&time=${appointmentTime}`;
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            chairsData = data;
            renderChairs();
        })
        .catch(error => {
            console.error('Error loading available chairs:', error);
            // Fallback to all chairs
            loadChairsData();
        });
}

function updateAvailableSlots() {
    const barberId = document.getElementById('barberId').value;
    const appointmentDate = document.getElementById('appointmentDate').value;

    if (!barberId || !appointmentDate) {
        return;
    }

    fetch(`/api/appointments/available-slots?date=${appointmentDate}&barber_id=${barberId}`)
        .then(response => response.json())
        .then(data => {
            const timeSelect = document.getElementById('appointmentTime');
            timeSelect.innerHTML = '<option value="">اختر الوقت...</option>';

            data.available_slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot;
                option.textContent = slot;
                timeSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading available slots:', error);
        });
}

function updateTotalAmount() {
    let totalAmount = 0;
    let totalDuration = 0;

    document.querySelectorAll('#servicesSelection input[type="checkbox"]:checked').forEach(checkbox => {
        totalAmount += parseFloat(checkbox.dataset.price);
        totalDuration += parseInt(checkbox.dataset.duration);
    });

    document.getElementById('totalAmount').value = totalAmount.toFixed(2);
    document.getElementById('estimatedDuration').value = totalDuration;

    updateAppointmentSummary();
}

function updateAppointmentSummary() {
    const customerId = document.getElementById('customerId').value;
    const barberId = document.getElementById('barberId').value;
    const appointmentDate = document.getElementById('appointmentDate').value;
    const appointmentTime = document.getElementById('appointmentTime').value;
    const totalAmount = document.getElementById('totalAmount').value;

    if (!customerId || !barberId || !selectedChair) {
        document.getElementById('appointmentSummary').style.display = 'none';
        return;
    }

    const customerName = document.getElementById('customerId').options[document.getElementById('customerId').selectedIndex].text.split(' - ')[0];
    const barberName = document.getElementById('barberId').options[document.getElementById('barberId').selectedIndex].text;

    const selectedServices = [];
    document.querySelectorAll('#servicesSelection input[type="checkbox"]:checked').forEach(checkbox => {
        const label = document.querySelector(`label[for="${checkbox.id}"]`).textContent;
        selectedServices.push(label);
    });

    const summaryContent = document.getElementById('summaryContent');
    summaryContent.innerHTML = `
        <div class="mb-2">
            <strong>العميل:</strong> ${customerName}
        </div>
        <div class="mb-2">
            <strong>الحلاق:</strong> ${barberName}
        </div>
        <div class="mb-2">
            <strong>الكرسي:</strong> رقم ${selectedChair.number}
        </div>
        ${appointmentDate ? `<div class="mb-2"><strong>التاريخ:</strong> ${appointmentDate}</div>` : ''}
        ${appointmentTime ? `<div class="mb-2"><strong>الوقت:</strong> ${appointmentTime}</div>` : ''}
        ${selectedServices.length > 0 ? `
            <div class="mb-2">
                <strong>الخدمات:</strong>
                <ul class="mb-0">
                    ${selectedServices.map(service => `<li>${service}</li>`).join('')}
                </ul>
            </div>
        ` : ''}
        ${totalAmount ? `<div class="mb-0"><strong>المبلغ الإجمالي:</strong> ${totalAmount} ر.ق</div>` : ''}
    `;

    document.getElementById('appointmentSummary').style.display = 'block';
}

function saveAppointment() {
    // Validate required fields
    const customerId = document.getElementById('customerId').value;
    const barberId = document.getElementById('barberId').value;
    const appointmentDate = document.getElementById('appointmentDate').value;
    const appointmentTime = document.getElementById('appointmentTime').value;
    const chairId = document.getElementById('selectedChairId').value;

    if (!customerId) {
        showError('يرجى اختيار العميل');
        return;
    }

    if (!barberId) {
        showError('يرجى اختيار الحلاق');
        return;
    }

    if (!appointmentDate) {
        showError('يرجى اختيار التاريخ');
        return;
    }

    if (!appointmentTime) {
        showError('يرجى اختيار الوقت');
        return;
    }

    if (!chairId) {
        showError('يرجى اختيار الكرسي');
        return;
    }

    // Get selected services
    const selectedServices = [];
    document.querySelectorAll('#servicesSelection input[type="checkbox"]:checked').forEach(checkbox => {
        selectedServices.push(parseInt(checkbox.value));
    });

    if (selectedServices.length === 0) {
        showError('يرجى اختيار خدمة واحدة على الأقل');
        return;
    }

    // Prepare appointment data
    const appointmentData = {
        customer_id: parseInt(customerId),
        barber_id: parseInt(barberId),
        chair_id: parseInt(chairId),
        appointment_date: appointmentDate,
        appointment_time: appointmentTime,
        service_ids: selectedServices,
        total_amount: parseFloat(document.getElementById('totalAmount').value) || 0,
        notes: document.getElementById('appointmentNotes').value.trim() || null
    };

    // Send request
    makeRequest('/api/appointments', 'POST', appointmentData)
        .then(response => {
            if (response.success) {
                showSuccess('تم حفظ الموعد بنجاح');

                // Redirect to appointments list after a short delay
                setTimeout(() => {
                    window.location.href = '/appointments';
                }, 1500);
            } else {
                showError(response.message || 'حدث خطأ في حفظ الموعد');
            }
        })
        .catch(error => {
            console.error('Error saving appointment:', error);
            showError('حدث خطأ في حفظ الموعد');
        });
}

function showNewCustomerModal() {
    // Reset form
    document.getElementById('newCustomerForm').reset();

    // Show modal
    new bootstrap.Modal(document.getElementById('newCustomerModal')).show();
}

function saveNewCustomerQuick() {
    const name = document.getElementById('newCustomerName').value.trim();
    const phone = document.getElementById('newCustomerPhone').value.trim();
    const email = document.getElementById('newCustomerEmail').value.trim();

    if (!name || !phone) {
        showError('الاسم ورقم الهاتف مطلوبان');
        return;
    }

    const customerData = {
        name: name,
        phone: phone,
        email: email || null
    };

    makeRequest('/api/customers', 'POST', customerData)
        .then(response => {
            if (response.success) {
                showSuccess('تم إضافة العميل بنجاح');

                // Add to customer dropdown
                const select = document.getElementById('customerId');
                const option = document.createElement('option');
                option.value = response.customer_id;
                option.textContent = `${name} - ${phone}`;
                option.selected = true;
                select.appendChild(option);

                // Hide modal
                bootstrap.Modal.getInstance(document.getElementById('newCustomerModal')).hide();

                // Select the new customer
                selectCustomer(response.customer_id);
            } else {
                showError(response.message || 'حدث خطأ في إضافة العميل');
            }
        })
        .catch(error => {
            console.error('Error saving customer:', error);
            showError('حدث خطأ في إضافة العميل');
        });
}

// Helper functions
function makeRequest(url, method = 'GET', data = null) {
    const options = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        }
    };

    if (data) {
        options.body = JSON.stringify(data);
    }

    return fetch(url, options)
        .then(response => response.json());
}

function showSuccess(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    setTimeout(() => {
        const alert = container.querySelector('.alert-success');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}

function showError(message) {
    const alertHtml = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    setTimeout(() => {
        const alert = container.querySelector('.alert-danger');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function showInfoNotification(message) {
    const alertHtml = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <i class="fas fa-info-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    setTimeout(() => {
        const alert = container.querySelector('.alert-info');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}

// Customer action functions
function viewCustomerHistory(customerId) {
    window.open(`/customers/${customerId}/history`, '_blank');
}

function editCustomer(customerId) {
    window.open(`/customers/${customerId}/edit`, '_blank');
}
</script>
{% endblock %}

{% block extra_css %}
<style>
/* Chair Selection Styles */
.chair-selection {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.chair-item {
    width: 80px;
    height: 80px;
    border: 2px solid #ddd;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.chair-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.chair-item.available {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #28a745;
    color: #155724;
}

.chair-item.available:hover {
    background: linear-gradient(135deg, #28a745 0%, #34ce57 100%);
    color: white;
}

.chair-item.occupied {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #dc3545;
    color: #721c24;
    cursor: not-allowed;
}

.chair-item.maintenance {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffc107;
    color: #856404;
    cursor: not-allowed;
}

.chair-item.selected {
    background: linear-gradient(135deg, #8B1538 0%, #a91d47 100%);
    border-color: #8B1538;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 21, 56, 0.3);
}

.chair-number {
    font-size: 1.2rem;
    font-weight: bold;
}

.chair-status {
    font-size: 0.7rem;
    text-transform: uppercase;
}

/* Chair Legend */
.chair-legend {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    display: inline-block;
}

.chair-legend.available {
    background: #28a745;
}

.chair-legend.occupied {
    background: #dc3545;
}

.chair-legend.maintenance {
    background: #ffc107;
}

.chair-legend.selected {
    background: #8B1538;
}

/* Service Selection */
.form-check {
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.form-check:hover {
    background-color: #f8f9fa;
    border-color: var(--qatar-maroon);
}

.form-check-input:checked + .form-check-label {
    color: var(--qatar-maroon);
    font-weight: 500;
}

/* Customer and Chair Info Cards */
#customerInfoCard, #chairInfoCard {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
}

#customerInfoCard:hover, #chairInfoCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

#customerInfoCard .card-header, #chairInfoCard .card-header {
    background: linear-gradient(135deg, var(--qatar-maroon) 0%, #a91d47 100%);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
}

#customerInfoCard .card-header h5, #chairInfoCard .card-header h5 {
    margin: 0;
    font-weight: 600;
}

#customerInfoCard .card-body, #chairInfoCard .card-body {
    padding: 1.5rem;
    background: #fafafa;
}

/* Info Cards Animation */
.info-card-enter {
    opacity: 0;
    transform: translateY(-20px);
}

.info-card-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.4s ease;
}

/* Responsive */
@media (max-width: 768px) {
    .chair-item {
        width: 60px;
        height: 60px;
    }

    .chair-number {
        font-size: 1rem;
    }

    .chair-status {
        font-size: 0.6rem;
    }

    #customerInfoCard .card-body, #chairInfoCard .card-body {
        padding: 1rem;
    }
}
</style>
{% endblock %}
