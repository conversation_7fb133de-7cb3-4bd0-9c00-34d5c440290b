<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة الموعد - صالون الرجال قطر</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --qatar-maroon: #8B1538;
            --qatar-white: #FFFFFF;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }

        .receipt-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .receipt-header {
            background: linear-gradient(135deg, var(--qatar-maroon), #a91d42);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .receipt-body {
            padding: 30px;
        }

        .info-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid var(--qatar-maroon);
        }

        .services-table {
            margin: 20px 0;
        }

        .services-table th {
            background: var(--qatar-maroon);
            color: white;
            border: none;
        }

        .total-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #28a745;
        }

        .print-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }

        @media print {
            .print-btn {
                display: none;
            }
            
            body {
                background: white;
            }
            
            .receipt-container {
                box-shadow: none;
                margin: 0;
                max-width: none;
            }
        }

        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .barber-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chair-info {
            background: #fff3cd;
            padding: 10px;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <button class="btn btn-primary print-btn" onclick="window.print()">
        <i class="fas fa-print me-2"></i>
        طباعة الفاتورة
    </button>

    <div class="receipt-container">
        <!-- Header -->
        <div class="receipt-header">
            <h1 class="mb-3">
                <i class="fas fa-cut me-3"></i>
                صالون الرجال قطر
            </h1>
            <h3>فاتورة الموعد</h3>
            <p class="mb-0">رقم الموعد: #{{ appointment.id }}</p>
        </div>

        <!-- Body -->
        <div class="receipt-body">
            <!-- Appointment Info -->
            <div class="info-section">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الموعد
                </h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>التاريخ:</strong> {{ appointment.appointment_date.strftime('%Y-%m-%d') }}</p>
                        <p><strong>الوقت:</strong> {{ appointment.appointment_time.strftime('%H:%M') }}
                        {% if appointment.end_time %}
                        - {{ appointment.end_time.strftime('%H:%M') }}
                        {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>الحالة:</strong> 
                            <span class="status-badge status-completed">
                                <i class="fas fa-check me-1"></i>
                                مكتمل
                            </span>
                        </p>
                        <p><strong>تاريخ الإنشاء:</strong> {{ appointment.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                </div>
            </div>

            <!-- Customer Info -->
            <div class="info-section">
                <h5 class="mb-3">
                    <i class="fas fa-user me-2"></i>
                    معلومات العميل
                </h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>الاسم:</strong> {{ appointment.customer.name }}
                        {% if appointment.customer.is_vip %}
                        <span class="badge bg-warning text-dark ms-2">VIP</span>
                        {% endif %}
                        </p>
                        <p><strong>الهاتف:</strong> {{ appointment.customer.phone }}</p>
                    </div>
                    <div class="col-md-6">
                        {% if appointment.customer.email %}
                        <p><strong>البريد الإلكتروني:</strong> {{ appointment.customer.email }}</p>
                        {% endif %}
                        {% if appointment.customer.preferred_barber_id == appointment.barber_id %}
                        <p><strong>نقاط الولاء:</strong> {{ appointment.customer.barber_loyalty_points }} نقطة</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Service Provider Info -->
            <div class="info-section">
                <h5 class="mb-3">
                    <i class="fas fa-user-tie me-2"></i>
                    معلومات مقدم الخدمة
                </h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="barber-info">
                            <i class="fas fa-scissors text-primary"></i>
                            <div>
                                <p class="mb-1"><strong>الحلاق:</strong> {{ appointment.barber.name }}</p>
                                {% if appointment.barber.specialties %}
                                <small class="text-muted">{{ appointment.barber.specialties }}</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chair-info">
                            <i class="fas fa-chair me-2"></i>
                            <strong>كرسي {{ appointment.chair.chair_number }}</strong>
                            {% if appointment.chair.name %}
                            - {{ appointment.chair.name }}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services -->
            <div class="info-section">
                <h5 class="mb-3">
                    <i class="fas fa-list me-2"></i>
                    الخدمات المقدمة
                </h5>
                
                {% if services_list %}
                <table class="table table-striped services-table">
                    <thead>
                        <tr>
                            <th>الخدمة</th>
                            <th>المدة</th>
                            <th>السعر</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for service in services_list %}
                        <tr>
                            <td>
                                {% if service.name is defined %}
                                    {{ service.name }}
                                {% else %}
                                    خدمة رقم {{ service }}
                                {% endif %}
                            </td>
                            <td>
                                {% if service.duration_minutes is defined %}
                                    {{ service.duration_minutes }} دقيقة
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                            <td>
                                {% if service.price is defined %}
                                    {{ "%.2f"|format(service.price) }} ر.ق
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-info-circle text-muted me-2"></i>
                    <span class="text-muted">{{ appointment.services or 'لا توجد تفاصيل خدمات متاحة' }}</span>
                </div>
                {% endif %}
            </div>

            <!-- Total -->
            {% if appointment.total_amount %}
            <div class="total-section text-center">
                <h4 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    المبلغ الإجمالي: <span class="text-success">{{ "%.2f"|format(appointment.total_amount) }} ر.ق</span>
                </h4>
            </div>
            {% endif %}

            <!-- Notes -->
            {% if appointment.notes %}
            <div class="info-section mt-4">
                <h5 class="mb-3">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات
                </h5>
                <p class="mb-0">{{ appointment.notes }}</p>
            </div>
            {% endif %}

            <!-- Footer -->
            <div class="text-center mt-4 pt-4 border-top">
                <p class="text-muted mb-2">شكراً لزيارتكم صالون الرجال قطر</p>
                <p class="text-muted mb-0">
                    <i class="fas fa-phone me-2"></i>
                    للاستفسارات: +974 XXXX XXXX
                </p>
            </div>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
