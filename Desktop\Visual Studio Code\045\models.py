from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime, date, time
from werkzeug.security import generate_password_hash, check_password_hash
import enum

db = SQLAlchemy()

class UserRole(enum.Enum):
    MANAGER = "manager"
    EMPLOYEE = "employee"
    ACCOUNTANT = "accountant"
    BARBER = "barber"

class ChairStatus(enum.Enum):
    AVAILABLE = "available"
    OCCUPIED = "occupied"
    MAINTENANCE = "maintenance"
    VACATION = "vacation"

class AppointmentStatus(enum.Enum):
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    NO_SHOW = "no_show"

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    full_name_ar = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    role = db.Column(db.Enum(UserRole), nullable=False, default=UserRole.EMPLOYEE)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

class Customer(db.Model):
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))
    phone = db.Column(db.String(20), unique=True, nullable=False)
    email = db.Column(db.String(120))
    date_of_birth = db.Column(db.Date)
    address = db.Column(db.Text)
    notes = db.Column(db.Text)
    visit_count = db.Column(db.Integer, default=0)
    total_spent = db.Column(db.Float, default=0.0)
    last_visit = db.Column(db.DateTime)
    is_vip = db.Column(db.Boolean, default=False)
    discount_percentage = db.Column(db.Float, default=0.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    appointments = db.relationship('Appointment', backref='customer', lazy=True)
    invoices = db.relationship('Invoice', backref='customer', lazy=True)
    
    def __repr__(self):
        return f'<Customer {self.name}>'

class Barber(db.Model):
    __tablename__ = 'barbers'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    hire_date = db.Column(db.Date, nullable=False)
    salary = db.Column(db.Float)
    commission_rate = db.Column(db.Float, default=0.0)  # Percentage
    specialties = db.Column(db.Text)  # JSON string of specialties
    working_hours_start = db.Column(db.Time, default=time(8, 0))
    working_hours_end = db.Column(db.Time, default=time(20, 0))
    working_days = db.Column(db.String(20), default='1,2,3,4,5,6,7')  # Days of week
    is_active = db.Column(db.Boolean, default=True)
    rating = db.Column(db.Float, default=0.0)
    total_services = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    chairs = db.relationship('Chair', backref='barber', lazy=True)
    appointments = db.relationship('Appointment', backref='barber', lazy=True)
    
    def __repr__(self):
        return f'<Barber {self.name}>'

class Chair(db.Model):
    __tablename__ = 'chairs'

    id = db.Column(db.Integer, primary_key=True)
    chair_number = db.Column(db.Integer, unique=True, nullable=False)
    name = db.Column(db.String(50))  # e.g., "كرسي رقم 1"
    barber_id = db.Column(db.Integer, db.ForeignKey('barbers.id'))
    status = db.Column(db.Enum(ChairStatus), default=ChairStatus.AVAILABLE)
    is_active = db.Column(db.Boolean, default=True)
    last_maintenance = db.Column(db.DateTime)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    appointments = db.relationship('Appointment', backref='chair', lazy=True)

    def __repr__(self):
        return f'<Chair {self.chair_number}>'

class Service(db.Model):
    __tablename__ = 'services'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    description_ar = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    duration_minutes = db.Column(db.Integer, default=30)
    category = db.Column(db.String(50))  # حلاقة، حلاقة ذقن، تنظيف بشرة، صبغة
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Service {self.name}>'

class Appointment(db.Model):
    __tablename__ = 'appointments'

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    barber_id = db.Column(db.Integer, db.ForeignKey('barbers.id'), nullable=False)
    chair_id = db.Column(db.Integer, db.ForeignKey('chairs.id'), nullable=False)
    appointment_date = db.Column(db.Date, nullable=False)
    appointment_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time)
    status = db.Column(db.Enum(AppointmentStatus), default=AppointmentStatus.SCHEDULED)
    services = db.Column(db.Text)  # JSON string of service IDs
    total_amount = db.Column(db.Float, default=0.0)
    notes = db.Column(db.Text)
    reminder_sent = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Appointment {self.id}>'

class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    appointment_id = db.Column(db.Integer, db.ForeignKey('appointments.id'))
    barber_id = db.Column(db.Integer, db.ForeignKey('barbers.id'))
    invoice_date = db.Column(db.DateTime, default=datetime.utcnow)
    services = db.Column(db.Text)  # JSON string of services and prices
    subtotal = db.Column(db.Float, nullable=False)
    discount_amount = db.Column(db.Float, default=0.0)
    tax_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20))  # نقدي، بطاقة، تحويل
    payment_status = db.Column(db.String(20), default='paid')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

class Product(db.Model):
    __tablename__ = 'products'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100))
    category = db.Column(db.String(50))  # جل، كريم، شفرات، مناديل
    brand = db.Column(db.String(50))
    current_stock = db.Column(db.Integer, default=0)
    minimum_stock = db.Column(db.Integer, default=5)
    unit_cost = db.Column(db.Float)
    supplier = db.Column(db.String(100))
    supplier_phone = db.Column(db.String(20))
    last_restocked = db.Column(db.DateTime)
    expiry_date = db.Column(db.Date)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    @property
    def is_low_stock(self):
        return self.current_stock <= self.minimum_stock

    def __repr__(self):
        return f'<Product {self.name}>'
