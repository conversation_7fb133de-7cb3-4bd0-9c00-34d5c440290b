{% extends "base.html" %}

{% block title %}إدارة المواعيد - صالون الرجال قطر{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="fas fa-calendar-alt me-2"></i>
                إدارة المواعيد
            </h1>
            <button class="btn btn-primary" onclick="newAppointment()">
                <i class="fas fa-plus me-2"></i>
                موعد جديد
            </button>
        </div>
    </div>
</div>

<!-- Date Selection and Filters -->
<div class="row mb-4">
    <div class="col-md-4">
        <label for="dateSelect" class="form-label">اختر التاريخ:</label>
        <input type="date" class="form-control" id="dateSelect" value="{{ selected_date }}" onchange="changeDate()">
    </div>
    <div class="col-md-4">
        <label for="statusFilter" class="form-label">حالة الموعد:</label>
        <select class="form-select" id="statusFilter" onchange="filterByStatus()">
            <option value="all">جميع الحالات</option>
            <option value="scheduled">مجدول</option>
            <option value="in_progress">جاري</option>
            <option value="completed">مكتمل</option>
            <option value="cancelled">ملغي</option>
        </select>
    </div>
    <div class="col-md-4">
        <label for="barberFilter" class="form-label">الحلاق:</label>
        <select class="form-select" id="barberFilter" onchange="filterByBarber()">
            <option value="all">جميع الحلاقين</option>
            <!-- Barbers will be loaded dynamically -->
        </select>
    </div>
</div>

<!-- Appointments Timeline -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-clock me-2"></i>
            مواعيد يوم {{ selected_date }}
        </h5>
    </div>
    <div class="card-body">
        {% if appointments %}
        <div class="timeline">
            {% for appointment in appointments %}
            <div class="timeline-item appointment-item" data-status="{{ appointment.status.value }}">
                <div class="timeline-time">
                    {{ appointment.appointment_time.strftime('%H:%M') }}
                    {% if appointment.end_time %}
                    - {{ appointment.end_time.strftime('%H:%M') }}
                    {% endif %}
                </div>
                <div class="timeline-content">
                    <div class="appointment-card">
                        <div class="row">
                            <div class="col-md-3">
                                <h6 class="mb-1">
                                    {{ appointment.customer.name }}
                                    {% if appointment.customer.preferred_barber_id == appointment.barber_id %}
                                    <span class="badge bg-success ms-1" title="الحلاق المفضل">
                                        <i class="fas fa-heart"></i>
                                    </span>
                                    {% endif %}
                                    {% if appointment.customer.is_vip %}
                                    <span class="badge bg-warning text-dark ms-1">VIP</span>
                                    {% endif %}
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ appointment.customer.phone }}
                                </small>
                                {% if appointment.customer.preferred_barber_id == appointment.barber_id %}
                                <br><small class="text-success">
                                    <i class="fas fa-star me-1"></i>
                                    {{ appointment.customer.barber_loyalty_points }} نقطة ولاء
                                </small>
                                {% endif %}
                            </div>
                            <div class="col-md-2">
                                <strong>{{ appointment.barber.name }}</strong>
                                <br>
                                <small class="text-muted">كرسي {{ appointment.chair.chair_number }}</small>
                            </div>
                            <div class="col-md-3">
                                {% if appointment.services %}
                                <small class="text-muted">الخدمات:</small>
                                <div class="services-list">
                                    <div id="services-{{ appointment.id }}" class="services-container">
                                        <!-- Services will be loaded by JavaScript -->
                                    </div>
                                </div>
                                <script>
                                    // Parse and display services for appointment {{ appointment.id }}
                                    try {
                                        const services = JSON.parse('{{ appointment.services|safe }}');
                                        const container = document.getElementById('services-{{ appointment.id }}');
                                        if (services && services.length > 0) {
                                            container.innerHTML = services.map(service =>
                                                `<span class="badge bg-light text-dark me-1 mb-1">${service.name}</span>`
                                            ).join('');
                                        } else {
                                            container.innerHTML = '<span class="text-muted">لا توجد خدمات محددة</span>';
                                        }
                                    } catch (e) {
                                        document.getElementById('services-{{ appointment.id }}').innerHTML =
                                            '<span class="text-muted">{{ appointment.services }}</span>';
                                    }
                                </script>
                                {% endif %}
                            </div>
                            <div class="col-md-2">
                                <div class="status-container mb-2">
                                    {% if appointment.status.value == 'scheduled' %}
                                    <span class="badge bg-primary fs-6">
                                        <i class="fas fa-clock me-1"></i>مجدول
                                    </span>
                                    {% elif appointment.status.value == 'in_progress' %}
                                    <span class="badge bg-warning text-dark fs-6">
                                        <i class="fas fa-play me-1"></i>جاري
                                    </span>
                                    {% elif appointment.status.value == 'completed' %}
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check me-1"></i>مكتمل
                                    </span>
                                    {% elif appointment.status.value == 'cancelled' %}
                                    <span class="badge bg-danger fs-6">
                                        <i class="fas fa-times me-1"></i>ملغي
                                    </span>
                                    {% elif appointment.status.value == 'no_show' %}
                                    <span class="badge bg-secondary fs-6">
                                        <i class="fas fa-user-times me-1"></i>لم يحضر
                                    </span>
                                    {% endif %}
                                </div>
                                {% if appointment.total_amount %}
                                <div class="amount-container">
                                    <strong class="text-success">
                                        <i class="fas fa-money-bill me-1"></i>
                                        {{ "%.2f"|format(appointment.total_amount) }} ر.ق
                                    </strong>
                                </div>
                                {% endif %}
                            </div>
                            <div class="col-md-2">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary"
                                            onclick="viewAppointment({{ appointment.id }})"
                                            data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if appointment.status.value == 'scheduled' %}
                                    <button type="button" class="btn btn-outline-success"
                                            onclick="startAppointment({{ appointment.id }})"
                                            data-bs-toggle="tooltip" title="بدء الخدمة">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger"
                                            onclick="cancelAppointment({{ appointment.id }})"
                                            data-bs-toggle="tooltip" title="إلغاء الموعد">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% elif appointment.status.value == 'in_progress' %}
                                    <button type="button" class="btn btn-outline-info"
                                            onclick="completeAppointment({{ appointment.id }})"
                                            data-bs-toggle="tooltip" title="إنهاء الخدمة">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning"
                                            onclick="pauseAppointment({{ appointment.id }})"
                                            data-bs-toggle="tooltip" title="إيقاف مؤقت">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    {% elif appointment.status.value == 'completed' %}
                                    <button type="button" class="btn btn-outline-success"
                                            onclick="printReceipt({{ appointment.id }})"
                                            data-bs-toggle="tooltip" title="طباعة الفاتورة">
                                        <i class="fas fa-print"></i>
                                    </button>
                                    {% endif %}
                                    {% if appointment.status.value in ['scheduled', 'in_progress'] %}
                                    <button type="button" class="btn btn-outline-warning"
                                            onclick="sendReminder({{ appointment.id }})"
                                            data-bs-toggle="tooltip" title="إرسال تذكير">
                                        <i class="fab fa-whatsapp"></i>
                                    </button>
                                    {% endif %}
                                    {% if appointment.status.value == 'scheduled' %}
                                    <button type="button" class="btn btn-outline-secondary"
                                            onclick="editAppointment({{ appointment.id }})"
                                            data-bs-toggle="tooltip" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% if appointment.notes %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-sticky-note me-1"></i>
                                    {{ appointment.notes }}
                                </small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد مواعيد في هذا التاريخ</h4>
            <p class="text-muted">ابدأ بإضافة مواعيد جديدة</p>
            <button class="btn btn-primary" onclick="newAppointment()">
                <i class="fas fa-plus me-2"></i>
                إضافة موعد جديد
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <div class="d-flex justify-content-center align-items-center mb-2">
                    <i class="fas fa-clock fa-2x text-primary me-2"></i>
                    <h3 class="text-primary mb-0">{{ appointments|selectattr("status.value", "equalto", "scheduled")|list|length }}</h3>
                </div>
                <p class="mb-0 fw-bold">مواعيد مجدولة</p>
                <small class="text-muted">في انتظار البدء</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <div class="d-flex justify-content-center align-items-center mb-2">
                    <i class="fas fa-play fa-2x text-warning me-2"></i>
                    <h3 class="text-warning mb-0">{{ appointments|selectattr("status.value", "equalto", "in_progress")|list|length }}</h3>
                </div>
                <p class="mb-0 fw-bold">مواعيد جارية</p>
                <small class="text-muted">قيد التنفيذ</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <div class="d-flex justify-content-center align-items-center mb-2">
                    <i class="fas fa-check fa-2x text-success me-2"></i>
                    <h3 class="text-success mb-0">{{ appointments|selectattr("status.value", "equalto", "completed")|list|length }}</h3>
                </div>
                <p class="mb-0 fw-bold">مواعيد مكتملة</p>
                <small class="text-muted">تم الانتهاء</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <div class="d-flex justify-content-center align-items-center mb-2">
                    <i class="fas fa-times fa-2x text-danger me-2"></i>
                    <h3 class="text-danger mb-0">{{ appointments|selectattr("status.value", "equalto", "cancelled")|list|length }}</h3>
                </div>
                <p class="mb-0 fw-bold">مواعيد ملغية</p>
                <small class="text-muted">تم الإلغاء</small>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Summary -->
<div class="row mt-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <h5 class="text-success">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            إجمالي الإيرادات اليوم
                        </h5>
                        <h4 class="text-success">
                            {% set total_revenue = appointments|selectattr("status.value", "equalto", "completed")|map(attribute="total_amount")|sum %}
                            {{ "%.2f"|format(total_revenue) }} ر.ق
                        </h4>
                    </div>
                    <div class="col-md-4">
                        <h5 class="text-info">
                            <i class="fas fa-users me-2"></i>
                            عدد العملاء المخدومين
                        </h5>
                        <h4 class="text-info">{{ appointments|selectattr("status.value", "equalto", "completed")|list|length }}</h4>
                    </div>
                    <div class="col-md-4">
                        <h5 class="text-primary">
                            <i class="fas fa-percentage me-2"></i>
                            معدل الإنجاز
                        </h5>
                        <h4 class="text-primary">
                            {% set total_appointments = appointments|length %}
                            {% set completed_appointments = appointments|selectattr("status.value", "equalto", "completed")|list|length %}
                            {% if total_appointments > 0 %}
                                {{ "%.1f"|format((completed_appointments / total_appointments) * 100) }}%
                            {% else %}
                                0%
                            {% endif %}
                        </h4>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Appointment Modal -->
<div class="modal fade" id="newAppointmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إضافة موعد جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="newAppointmentForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentCustomer" class="form-label">العميل *</label>
                                <select class="form-select" id="appointmentCustomer" required>
                                    <option value="">اختر العميل</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentBarber" class="form-label">الحلاق *</label>
                                <select class="form-select" id="appointmentBarber" required>
                                    <option value="">اختر الحلاق</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="appointmentDate" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentTime" class="form-label">الوقت *</label>
                                <input type="time" class="form-control" id="appointmentTime" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="appointmentChair" class="form-label">الكرسي *</label>
                        <select class="form-select" id="appointmentChair" required>
                            <option value="">اختر الكرسي</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الخدمات *</label>
                        <div id="appointmentServices" class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                            <!-- Services will be loaded here -->
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentTotal" class="form-label">المبلغ الإجمالي (ر.ق)</label>
                                <input type="number" class="form-control" id="appointmentTotal" step="0.01" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="appointmentNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="appointmentNotes" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveNewAppointment()">
                    <i class="fas fa-save me-2"></i>
                    حفظ الموعد
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--qatar-maroon);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -23px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--qatar-maroon);
    border: 3px solid white;
    box-shadow: 0 0 0 3px var(--qatar-maroon);
}

.timeline-time {
    font-weight: bold;
    color: var(--qatar-maroon);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.appointment-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    border-left: 4px solid var(--qatar-maroon);
    transition: all 0.3s ease;
}

.appointment-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.services-list {
    font-size: 0.9rem;
}

/* Status-based styling */
.appointment-item[data-status="scheduled"] .appointment-card {
    border-left-color: #007bff;
}

.appointment-item[data-status="in_progress"] .appointment-card {
    border-left-color: #ffc107;
    background: #fff8e1;
}

.appointment-item[data-status="completed"] .appointment-card {
    border-left-color: #28a745;
    background: #f0fff4;
}

.appointment-item[data-status="cancelled"] .appointment-card {
    border-left-color: #dc3545;
    background: #fff5f5;
    opacity: 0.8;
}

/* Enhanced card animations */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Status badges enhancement */
.badge {
    font-size: 0.85rem;
    padding: 6px 12px;
}

/* Button group enhancements */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-group-sm .btn:hover {
    transform: scale(1.05);
}

/* Timeline enhancements */
.timeline-item:hover .appointment-card {
    border-left-width: 6px;
}

/* Loading animation */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Success/Error message styling */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Revenue summary styling */
.card-body h5 {
    font-weight: 600;
    margin-bottom: 10px;
}

.card-body h4 {
    font-weight: 700;
    font-size: 1.5rem;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .btn-group-sm {
        flex-direction: column;
    }

    .btn-group-sm .btn {
        margin-bottom: 2px;
        border-radius: 4px !important;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline::before {
        left: 10px;
    }

    .timeline-item::before {
        left: -18px;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function changeDate() {
    const selectedDate = document.getElementById('dateSelect').value;
    window.location.href = `{{ url_for('appointments') }}?date=${selectedDate}`;
}

function filterByStatus() {
    const status = document.getElementById('statusFilter').value;
    const items = document.querySelectorAll('.appointment-item');
    
    items.forEach(item => {
        if (status === 'all' || item.dataset.status === status) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

function filterByBarber() {
    const barberId = document.getElementById('barberFilter').value;
    const items = document.querySelectorAll('.appointment-item');

    items.forEach(item => {
        const appointmentCard = item.querySelector('.appointment-card');
        const barberName = appointmentCard.querySelector('.col-md-2 strong').textContent.trim();

        if (barberId === 'all') {
            item.style.display = 'block';
        } else {
            // Get barber name from the selected option
            const selectedOption = document.querySelector(`#barberFilter option[value="${barberId}"]`);
            const selectedBarberName = selectedOption ? selectedOption.textContent : '';

            if (barberName === selectedBarberName) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        }
    });
}

function newAppointment() {
    showNewAppointmentModal();
}

function viewAppointment(appointmentId) {
    window.location.href = `/appointments/${appointmentId}`;
}

function editAppointment(appointmentId) {
    window.location.href = `/appointments/${appointmentId}/edit`;
}

function startAppointment(appointmentId) {
    if (confirm('هل تريد بدء هذا الموعد؟')) {
        // Add loading state
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        makeRequest(`/api/appointments/${appointmentId}/start`, 'POST')
            .then(response => {
                if (response.success) {
                    showSuccess('تم بدء الموعد بنجاح');
                    // Play success sound
                    playNotificationSound('success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showError(response.message || 'حدث خطأ في بدء الموعد');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                showError('حدث خطأ في بدء الموعد');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
    }
}

function completeAppointment(appointmentId) {
    if (confirm('هل تريد إنهاء هذا الموعد؟')) {
        // Add loading state
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        makeRequest(`/api/appointments/${appointmentId}/complete`, 'POST')
            .then(response => {
                if (response.success) {
                    showSuccess('تم إنهاء الموعد بنجاح');
                    playNotificationSound('success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showError(response.message || 'حدث خطأ في إنهاء الموعد');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                showError('حدث خطأ في إنهاء الموعد');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
    }
}

function sendReminder(appointmentId) {
    if (confirm('هل تريد إرسال تذكير واتساب للعميل؟')) {
        makeRequest(`/api/appointments/${appointmentId}/reminder`, 'POST')
            .then(response => {
                if (response.success) {
                    showSuccess('تم إرسال التذكير بنجاح عبر واتساب');
                } else {
                    showError(response.message || 'حدث خطأ في إرسال التذكير');
                }
            })
            .catch(error => {
                showError('حدث خطأ في إرسال التذكير');
            });
    }
}

function cancelAppointment(appointmentId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الموعد؟')) {
        makeRequest(`/api/appointments/${appointmentId}/cancel`, 'POST')
            .then(response => {
                if (response.success) {
                    showSuccess('تم إلغاء الموعد بنجاح');
                    location.reload();
                } else {
                    showError(response.message || 'حدث خطأ في إلغاء الموعد');
                }
            })
            .catch(error => {
                showError('حدث خطأ في إلغاء الموعد');
            });
    }
}

function pauseAppointment(appointmentId) {
    if (confirm('هل تريد إيقاف هذا الموعد مؤقتاً؟')) {
        makeRequest(`/api/appointments/${appointmentId}/pause`, 'POST')
            .then(response => {
                if (response.success) {
                    showSuccess('تم إيقاف الموعد مؤقتاً');
                    location.reload();
                } else {
                    showError(response.message || 'حدث خطأ في إيقاف الموعد');
                }
            })
            .catch(error => {
                showError('حدث خطأ في إيقاف الموعد');
            });
    }
}

function printReceipt(appointmentId) {
    window.open(`/appointments/${appointmentId}/receipt`, '_blank');
}

function showNewAppointmentModal() {
    new bootstrap.Modal(document.getElementById('newAppointmentModal')).show();

    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('appointmentDate').value = today;

    loadCustomersForAppointment();
    loadBarbersForAppointment();
    loadServicesForAppointment();
    loadAvailableChairs();
}

function loadCustomersForAppointment() {
    makeRequest('/api/customers', 'GET')
        .then(response => {
            const select = document.getElementById('appointmentCustomer');
            select.innerHTML = '<option value="">اختر العميل</option>';

            response.customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = customer.name;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading customers:', error);
        });
}

function loadBarbersForAppointment() {
    makeRequest('/api/barbers/active', 'GET')
        .then(response => {
            const select = document.getElementById('appointmentBarber');
            select.innerHTML = '<option value="">اختر الحلاق</option>';

            response.barbers.forEach(barber => {
                const option = document.createElement('option');
                option.value = barber.id;
                option.textContent = barber.name;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading barbers:', error);
        });
}

function loadServicesForAppointment() {
    makeRequest('/api/services', 'GET')
        .then(response => {
            const container = document.getElementById('appointmentServices');
            container.innerHTML = '';

            response.services.forEach(service => {
                const div = document.createElement('div');
                div.className = 'form-check';
                div.innerHTML = `
                    <input class="form-check-input" type="checkbox" value="${service.id}"
                           id="service_${service.id}" onchange="calculateTotal()">
                    <label class="form-check-label" for="service_${service.id}">
                        ${service.name} - ${service.price} ر.ق (${service.duration_minutes} دقيقة)
                    </label>
                `;
                container.appendChild(div);
            });
        })
        .catch(error => {
            console.error('Error loading services:', error);
        });
}

function loadAvailableChairs() {
    makeRequest('/api/chairs/status', 'GET')
        .then(response => {
            const select = document.getElementById('appointmentChair');
            select.innerHTML = '<option value="">اختر الكرسي</option>';

            response.forEach(chair => {
                if (chair.status === 'AVAILABLE') {
                    const option = document.createElement('option');
                    option.value = chair.id;
                    option.textContent = `كرسي ${chair.chair_number} - ${chair.name}`;
                    select.appendChild(option);
                }
            });
        })
        .catch(error => {
            console.error('Error loading chairs:', error);
        });
}

function calculateTotal() {
    const checkboxes = document.querySelectorAll('#appointmentServices input[type="checkbox"]:checked');
    let total = 0;

    checkboxes.forEach(checkbox => {
        const label = document.querySelector(`label[for="${checkbox.id}"]`);
        const priceMatch = label.textContent.match(/(\d+(?:\.\d+)?)\s*ر\.ق/);
        if (priceMatch) {
            total += parseFloat(priceMatch[1]);
        }
    });

    document.getElementById('appointmentTotal').value = total.toFixed(2);
}

function saveNewAppointment() {
    const customerId = document.getElementById('appointmentCustomer').value;
    const barberId = document.getElementById('appointmentBarber').value;
    const chairId = document.getElementById('appointmentChair').value;
    const appointmentDate = document.getElementById('appointmentDate').value;
    const appointmentTime = document.getElementById('appointmentTime').value;
    const notes = document.getElementById('appointmentNotes').value;
    const totalAmount = document.getElementById('appointmentTotal').value;

    // Get selected services
    const selectedServices = [];
    document.querySelectorAll('#appointmentServices input[type="checkbox"]:checked').forEach(checkbox => {
        selectedServices.push(parseInt(checkbox.value));
    });

    if (!customerId || !barberId || !chairId || !appointmentDate || !appointmentTime) {
        showError('جميع الحقول الأساسية مطلوبة');
        return;
    }

    if (selectedServices.length === 0) {
        showError('يجب اختيار خدمة واحدة على الأقل');
        return;
    }

    const data = {
        customer_id: parseInt(customerId),
        barber_id: parseInt(barberId),
        chair_id: parseInt(chairId),
        appointment_date: appointmentDate,
        appointment_time: appointmentTime,
        services: selectedServices,
        total_amount: parseFloat(totalAmount),
        notes: notes
    };

    makeRequest('/api/appointments', 'POST', data)
        .then(response => {
            if (response.success) {
                showSuccess('تم حجز الموعد بنجاح');
                bootstrap.Modal.getInstance(document.getElementById('newAppointmentModal')).hide();
                document.getElementById('newAppointmentForm').reset();

                // Reload page to show new appointment
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showError(response.message);
            }
        })
        .catch(error => {
            showError('حدث خطأ في حجز الموعد');
        });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Load barbers for filter
    loadBarbersForFilter();

    // Auto-refresh every 30 seconds
    setInterval(function() {
        if (!document.querySelector('.modal.show')) { // Don't refresh if modal is open
            location.reload();
        }
    }, 30000);

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            newAppointment();
        }
    });
});

function loadBarbersForFilter() {
    makeRequest('/api/barbers/active', 'GET')
        .then(response => {
            const select = document.getElementById('barberFilter');

            response.barbers.forEach(barber => {
                const option = document.createElement('option');
                option.value = barber.id;
                option.textContent = barber.name;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading barbers for filter:', error);
        });
}

// Notification sound function
function playNotificationSound(type) {
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        if (type === 'success') {
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);
        } else if (type === 'error') {
            oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(300, audioContext.currentTime + 0.1);
        }

        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.2);
    } catch (e) {
        // Fallback for browsers that don't support Web Audio API
        console.log('Audio notification not supported');
    }
}

// Enhanced success/error messages
function showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-exclamation-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
