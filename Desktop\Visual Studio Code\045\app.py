from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_migrate import Migrate
from werkzeug.security import generate_password_hash
from datetime import datetime, date, time, timedelta
import os
import json

from config import config
from models import db, User, Customer, Barber, Chair, Service, Appointment, Invoice, Product
from models import UserRole, ChairStatus, AppointmentStatus

def create_app(config_name=None):
    app = Flask(__name__)
    
    # Configuration
    config_name = config_name or os.environ.get('FLASK_CONFIG', 'development')
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # Login Manager
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Create tables and sample data
    with app.app_context():
        db.create_all()
        create_sample_data()
    
    # Routes
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return render_template('index.html')
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form['username']
            password = request.form['password']
            user = User.query.filter_by(username=username).first()
            
            if user and user.check_password(password):
                login_user(user)
                user.last_login = datetime.utcnow()
                db.session.commit()
                flash('تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
        
        return render_template('auth/login.html')
    
    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash('تم تسجيل الخروج بنجاح', 'info')
        return redirect(url_for('index'))
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        # Get dashboard statistics
        today = date.today()
        
        stats = {
            'total_customers': Customer.query.count(),
            'total_barbers': Barber.query.filter_by(is_active=True).count(),
            'total_chairs': Chair.query.filter_by(is_active=True).count(),
            'today_appointments': Appointment.query.filter_by(appointment_date=today).count(),
            'available_chairs': Chair.query.filter_by(status=ChairStatus.AVAILABLE).count(),
            'occupied_chairs': Chair.query.filter_by(status=ChairStatus.OCCUPIED).count(),
        }
        
        # Recent appointments
        recent_appointments = Appointment.query.filter_by(appointment_date=today)\
            .order_by(Appointment.appointment_time.desc()).limit(5).all()
        
        # Low stock products
        low_stock_products = Product.query.filter(
            Product.current_stock <= Product.minimum_stock,
            Product.is_active == True
        ).all()
        
        return render_template('dashboard.html', 
                             stats=stats, 
                             recent_appointments=recent_appointments,
                             low_stock_products=low_stock_products)
    
    @app.route('/customers')
    @login_required
    def customers():
        page = request.args.get('page', 1, type=int)
        search = request.args.get('search', '')
        
        query = Customer.query
        if search:
            query = query.filter(
                (Customer.name.contains(search)) |
                (Customer.phone.contains(search))
            )
        
        customers = query.paginate(
            page=page, per_page=10, error_out=False
        )
        
        return render_template('customers/list.html', customers=customers, search=search)
    
    @app.route('/appointments')
    @login_required
    def appointments():
        selected_date = request.args.get('date', date.today().isoformat())
        appointments = Appointment.query.filter_by(
            appointment_date=datetime.strptime(selected_date, '%Y-%m-%d').date()
        ).order_by(Appointment.appointment_time).all()
        
        return render_template('appointments/list.html', 
                             appointments=appointments, 
                             selected_date=selected_date)
    
    @app.route('/chairs')
    @login_required
    def chairs():
        chairs = Chair.query.filter_by(is_active=True).order_by(Chair.chair_number).all()
        return render_template('chairs/list.html', chairs=chairs)
    
    @app.route('/services')
    @login_required
    def services():
        services = Service.query.filter_by(is_active=True).all()
        return render_template('services/list.html', services=services)
    
    @app.route('/inventory')
    @login_required
    def inventory():
        products = Product.query.filter_by(is_active=True).all()
        return render_template('inventory/list.html', products=products)
    
    @app.route('/reports')
    @login_required
    def reports():
        return render_template('reports/index.html')

    @app.route('/reports/preferred-barber-analysis')
    @login_required
    def preferred_barber_analysis():
        return render_template('reports/preferred_barber_analysis.html')

    @app.route('/reports/staff-performance')
    @login_required
    def staff_performance():
        return render_template('reports/staff_performance.html')

    @app.route('/profile')
    @login_required
    def profile():
        return render_template('profile.html')

    @app.route('/settings')
    @login_required
    def settings():
        return render_template('settings.html')

    @app.route('/staff')
    @login_required
    def staff_management():
        return render_template('staff/index.html')

    @app.route('/staff/<int:staff_id>')
    @login_required
    def staff_details(staff_id):
        return render_template('staff/performance_details.html', staff_id=staff_id)

    @app.route('/staff/<int:staff_id>/evaluate')
    @login_required
    def staff_evaluate(staff_id):
        return render_template('staff/evaluate.html', staff_id=staff_id)
    
    # API Routes for AJAX
    @app.route('/api/chairs/status')
    @login_required
    def api_chairs_status():
        chairs = Chair.query.filter_by(is_active=True).all()
        return jsonify([{
            'id': chair.id,
            'number': chair.chair_number,
            'status': chair.status.value,
            'barber': chair.barber.name if chair.barber else None
        } for chair in chairs])

    # API for Preferred Barber Management
    @app.route('/api/barbers/active')
    @login_required
    def api_active_barbers():
        barbers = Barber.query.filter_by(is_active=True).all()
        return jsonify({
            'barbers': [{
                'id': barber.id,
                'name': barber.name,
                'name_ar': barber.name_ar,
                'specialties': barber.specialties,
                'rating': barber.rating
            } for barber in barbers]
        })

    @app.route('/api/customers/<int:customer_id>/preferences')
    @login_required
    def api_customer_preferences(customer_id):
        customer = Customer.query.get_or_404(customer_id)
        return jsonify({
            'preferred_barber_id': customer.preferred_barber_id,
            'barber_loyalty_points': customer.barber_loyalty_points,
            'preferred_barber_name': customer.preferred_barber.name if customer.preferred_barber else None
        })

    @app.route('/api/customers/<int:customer_id>/preferred-barber', methods=['POST'])
    @login_required
    def api_set_preferred_barber(customer_id):
        customer = Customer.query.get_or_404(customer_id)
        data = request.get_json()

        # Update preferred barber
        customer.preferred_barber_id = data.get('preferred_barber_id')
        customer.barber_loyalty_points = data.get('barber_loyalty_points', 0)

        # Create history record
        from models import CustomerBarberHistory
        history = CustomerBarberHistory(
            customer_id=customer_id,
            barber_id=data.get('preferred_barber_id'),
            service_notes=data.get('notes', 'تعيين كحلاق مفضل'),
            visit_date=datetime.utcnow()
        )

        db.session.add(history)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تعيين الحلاق المفضل بنجاح'})

    @app.route('/api/customers/<int:customer_id>/barber-history')
    @login_required
    def api_customer_barber_history(customer_id):
        from models import CustomerBarberHistory
        history = CustomerBarberHistory.query.filter_by(customer_id=customer_id)\
            .order_by(CustomerBarberHistory.visit_date.desc()).all()

        return jsonify({
            'history': [{
                'barber_name': record.barber.name,
                'visit_date': record.visit_date.strftime('%Y-%m-%d %H:%M'),
                'service_rating': record.service_rating,
                'service_notes': record.service_notes,
                'total_amount': record.total_amount,
                'services_provided': record.services_provided
            } for record in history]
        })

    @app.route('/api/customers/<int:customer_id>/loyalty-points', methods=['POST'])
    @login_required
    def api_update_loyalty_points(customer_id):
        customer = Customer.query.get_or_404(customer_id)
        data = request.get_json()

        points_to_add = data.get('points_to_add', 0)
        reason = data.get('reason', '')

        # Update loyalty points
        customer.barber_loyalty_points += points_to_add

        # Create history record
        from models import CustomerBarberHistory
        history = CustomerBarberHistory(
            customer_id=customer_id,
            barber_id=customer.preferred_barber_id,
            service_notes=f'إضافة {points_to_add} نقطة ولاء - {reason}',
            visit_date=datetime.utcnow()
        )

        db.session.add(history)
        db.session.commit()

        return jsonify({'success': True, 'new_points': customer.barber_loyalty_points})

    # Preferred Barber Reports API
    @app.route('/api/reports/preferred-barber-summary')
    @login_required
    def api_preferred_barber_summary():
        customers_with_preferred = Customer.query.filter(Customer.preferred_barber_id.isnot(None)).count()
        total_loyalty_points = db.session.query(db.func.sum(Customer.barber_loyalty_points)).scalar() or 0

        # Calculate average relationship duration (simplified)
        avg_duration = 30  # Placeholder - would need more complex calculation
        retention_rate = 85  # Placeholder - would need more complex calculation

        return jsonify({
            'total_customers_with_preferred': customers_with_preferred,
            'total_loyalty_points': int(total_loyalty_points),
            'average_relationship_duration': avg_duration,
            'retention_rate': retention_rate
        })

    @app.route('/api/reports/barber-preferred-performance')
    @login_required
    def api_barber_preferred_performance():
        barbers = Barber.query.filter_by(is_active=True).all()
        performance_data = []

        for barber in barbers:
            preferred_customers = Customer.query.filter_by(preferred_barber_id=barber.id).all()
            total_loyalty_points = sum(c.barber_loyalty_points for c in preferred_customers)
            total_revenue = sum(c.total_spent for c in preferred_customers)

            performance_data.append({
                'name': barber.name,
                'specialties': barber.specialties,
                'preferred_customers_count': len(preferred_customers),
                'total_loyalty_points': total_loyalty_points,
                'average_rating': barber.rating,
                'total_revenue': total_revenue,
                'retention_rate': 90  # Placeholder
            })

        return jsonify({'barbers': performance_data})

    @app.route('/api/reports/top-loyal-customers')
    @login_required
    def api_top_loyal_customers():
        customers = Customer.query.filter(Customer.preferred_barber_id.isnot(None))\
            .order_by(Customer.barber_loyalty_points.desc()).limit(10).all()

        return jsonify({
            'customers': [{
                'name': customer.name,
                'loyalty_points': customer.barber_loyalty_points,
                'visit_count': customer.visit_count,
                'preferred_barber_name': customer.preferred_barber.name if customer.preferred_barber else None
            } for customer in customers]
        })

    # Staff Performance API Endpoints
    @app.route('/api/reports/staff-performance-summary')
    @login_required
    def api_staff_performance_summary():
        period = request.args.get('period', 'month')
        staff_id = request.args.get('staff_id')

        # Calculate date range based on period
        today = datetime.now().date()
        if period == 'today':
            start_date = today
        elif period == 'week':
            start_date = today - timedelta(days=7)
        elif period == 'month':
            start_date = today - timedelta(days=30)
        elif period == 'quarter':
            start_date = today - timedelta(days=90)
        elif period == 'year':
            start_date = today - timedelta(days=365)
        else:
            start_date = today - timedelta(days=30)

        # Get active barbers
        barbers_query = Barber.query.filter_by(is_active=True)
        if staff_id:
            barbers_query = barbers_query.filter_by(id=staff_id)

        barbers = barbers_query.all()
        total_staff = len(barbers)

        # Calculate summary metrics
        total_revenue = 0
        total_performance_score = 0
        top_performer = None
        best_score = 0

        for barber in barbers:
            # Calculate revenue (simplified - would need actual performance data)
            barber_revenue = sum(c.total_spent for c in Customer.query.filter_by(preferred_barber_id=barber.id).all())
            total_revenue += barber_revenue

            # Calculate performance score (simplified)
            performance_score = barber.rating
            total_performance_score += performance_score

            if performance_score > best_score:
                best_score = performance_score
                top_performer = barber.name

        average_performance = total_performance_score / total_staff if total_staff > 0 else 0

        return jsonify({
            'total_staff': total_staff,
            'average_performance': average_performance,
            'top_performer': top_performer,
            'total_revenue': total_revenue
        })

    @app.route('/api/reports/staff-performance-detailed')
    @login_required
    def api_staff_performance_detailed():
        period = request.args.get('period', 'month')
        staff_id = request.args.get('staff_id')

        # Get active barbers
        barbers_query = Barber.query.filter_by(is_active=True)
        if staff_id:
            barbers_query = barbers_query.filter_by(id=staff_id)

        barbers = barbers_query.all()
        staff_performance = []

        for barber in barbers:
            # Get customers served by this barber
            customers = Customer.query.filter_by(preferred_barber_id=barber.id).all()
            customers_served = len(customers)
            repeat_customers = len([c for c in customers if c.visit_count > 1])

            # Calculate revenue
            total_revenue = sum(c.total_spent for c in customers)
            average_per_customer = total_revenue / customers_served if customers_served > 0 else 0

            # Performance metrics (simplified)
            overall_score = barber.rating
            customer_rating = barber.rating
            efficiency_percentage = min(90 + (barber.rating - 3) * 10, 100)
            on_time_percentage = min(85 + (barber.rating - 3) * 15, 100)

            # Goals (placeholder)
            goals_achieved = 2
            goals_pending = 1
            goals_completion_rate = 67

            staff_performance.append({
                'id': barber.id,
                'name': barber.name,
                'specialties': barber.specialties,
                'customers_served': customers_served,
                'repeat_customers': repeat_customers,
                'total_revenue': total_revenue,
                'average_per_customer': average_per_customer,
                'overall_score': overall_score,
                'customer_rating': customer_rating,
                'efficiency_percentage': efficiency_percentage,
                'on_time_percentage': on_time_percentage,
                'goals_achieved': goals_achieved,
                'goals_pending': goals_pending,
                'goals_completion_rate': goals_completion_rate
            })

        # Generate chart data (simplified)
        chart_data = {
            'trends': {
                'dates': ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05'],
                'performance_scores': [4.2, 4.3, 4.1, 4.4, 4.5],
                'satisfaction_scores': [4.0, 4.2, 4.1, 4.3, 4.4]
            },
            'monthly_comparison': {
                'staff_names': [b.name for b in barbers[:5]],
                'revenues': [sum(c.total_spent for c in Customer.query.filter_by(preferred_barber_id=b.id).all()) for b in barbers[:5]],
                'customers': [len(Customer.query.filter_by(preferred_barber_id=b.id).all()) for b in barbers[:5]]
            },
            'revenue_distribution': {
                'staff_names': [b.name for b in barbers[:5]],
                'revenues': [sum(c.total_spent for c in Customer.query.filter_by(preferred_barber_id=b.id).all()) for b in barbers[:5]]
            },
            'top_performers': [{
                'name': b.name,
                'score': b.rating,
                'revenue': sum(c.total_spent for c in Customer.query.filter_by(preferred_barber_id=b.id).all()),
                'customers': len(Customer.query.filter_by(preferred_barber_id=b.id).all())
            } for b in sorted(barbers, key=lambda x: x.rating, reverse=True)[:5]]
        }

        return jsonify({
            'staff_performance': staff_performance,
            **chart_data
        })

    @app.route('/api/staff-evaluations', methods=['POST'])
    @login_required
    def api_create_staff_evaluation():
        from models import StaffEvaluation
        data = request.get_json()

        evaluation = StaffEvaluation(
            staff_id=data['staff_id'],
            evaluator_id=current_user.id,
            evaluation_date=datetime.now().date(),
            evaluation_period_start=datetime.strptime(data['evaluationPeriodStart'], '%Y-%m-%d').date(),
            evaluation_period_end=datetime.strptime(data['evaluationPeriodEnd'], '%Y-%m-%d').date(),
            technical_skills=float(data['technicalSkills']),
            customer_service=float(data['customerService']),
            punctuality=float(data['punctuality']),
            teamwork=float(data['teamwork']),
            communication=float(data['communication']),
            professionalism=float(data['professionalism']),
            initiative=float(data['initiative']),
            overall_score=float(data['overall_score']),
            strengths=data.get('strengths', ''),
            areas_for_improvement=data.get('areas_for_improvement', ''),
            goals_next_period=data.get('goals_next_period', ''),
            status='COMPLETED'
        )

        db.session.add(evaluation)
        db.session.commit()

        return jsonify({'success': True, 'evaluation_id': evaluation.id})

    @app.route('/api/performance-goals')
    @login_required
    def api_performance_goals():
        from models import PerformanceGoal
        staff_id = request.args.get('staff_id')

        goals_query = PerformanceGoal.query
        if staff_id:
            goals_query = goals_query.filter_by(staff_id=staff_id)

        goals = goals_query.order_by(PerformanceGoal.created_at.desc()).all()

        return jsonify({
            'goals': [{
                'id': goal.id,
                'title': goal.title,
                'description': goal.description,
                'target_value': goal.target_value,
                'current_value': goal.current_value,
                'unit': goal.unit,
                'start_date': goal.start_date.strftime('%Y-%m-%d'),
                'end_date': goal.end_date.strftime('%Y-%m-%d'),
                'status': goal.status,
                'priority': goal.priority,
                'progress_percentage': goal.progress_percentage,
                'staff_name': goal.staff_member.name
            } for goal in goals]
        })

    # Appointments API Endpoints
    @app.route('/api/appointments', methods=['POST'])
    @login_required
    def api_create_appointment():
        data = request.get_json()

        try:
            # Parse date and time
            appointment_date = datetime.strptime(data['appointment_date'], '%Y-%m-%d').date()
            appointment_time = datetime.strptime(data['appointment_time'], '%H:%M').time()

            # Calculate end time based on services
            service_ids = data.get('services', [])
            total_duration = 0

            if service_ids:
                services = Service.query.filter(Service.id.in_(service_ids)).all()
                total_duration = sum(service.duration_minutes for service in services)
            else:
                total_duration = 30  # Default duration

            # Calculate end time
            start_datetime = datetime.combine(appointment_date, appointment_time)
            end_datetime = start_datetime + timedelta(minutes=total_duration)
            end_time = end_datetime.time()

            # Create appointment
            appointment = Appointment(
                customer_id=data['customer_id'],
                barber_id=data['barber_id'],
                chair_id=data['chair_id'],
                appointment_date=appointment_date,
                appointment_time=appointment_time,
                end_time=end_time,
                services=json.dumps(service_ids),
                total_amount=data.get('total_amount', 0.0),
                notes=data.get('notes', ''),
                status='SCHEDULED'
            )

            db.session.add(appointment)
            db.session.commit()

            return jsonify({
                'success': True,
                'appointment_id': appointment.id,
                'message': 'تم حجز الموعد بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في حجز الموعد: {str(e)}'
            }), 400

    @app.route('/api/appointments/<int:appointment_id>', methods=['PUT'])
    @login_required
    def api_update_appointment(appointment_id):
        appointment = Appointment.query.get_or_404(appointment_id)
        data = request.get_json()

        try:
            if 'appointment_date' in data:
                appointment.appointment_date = datetime.strptime(data['appointment_date'], '%Y-%m-%d').date()

            if 'appointment_time' in data:
                appointment.appointment_time = datetime.strptime(data['appointment_time'], '%H:%M').time()

            if 'barber_id' in data:
                appointment.barber_id = data['barber_id']

            if 'chair_id' in data:
                appointment.chair_id = data['chair_id']

            if 'status' in data:
                appointment.status = data['status']

            if 'notes' in data:
                appointment.notes = data['notes']

            if 'total_amount' in data:
                appointment.total_amount = data['total_amount']

            appointment.updated_at = datetime.utcnow()
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم تحديث الموعد بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في تحديث الموعد: {str(e)}'
            }), 400

    @app.route('/api/appointments/<int:appointment_id>', methods=['DELETE'])
    @login_required
    def api_delete_appointment(appointment_id):
        appointment = Appointment.query.get_or_404(appointment_id)

        try:
            db.session.delete(appointment)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم حذف الموعد بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في حذف الموعد: {str(e)}'
            }), 400

    @app.route('/api/appointments/available-slots')
    @login_required
    def api_available_slots():
        date_str = request.args.get('date')
        barber_id = request.args.get('barber_id')

        if not date_str:
            return jsonify({'error': 'التاريخ مطلوب'}), 400

        try:
            appointment_date = datetime.strptime(date_str, '%Y-%m-%d').date()

            # Get barber working hours
            barber = Barber.query.get(barber_id) if barber_id else None
            start_hour = 9  # Default start
            end_hour = 22   # Default end

            if barber and barber.working_hours_start and barber.working_hours_end:
                start_hour = barber.working_hours_start.hour
                end_hour = barber.working_hours_end.hour

            # Generate time slots (30-minute intervals)
            available_slots = []
            current_time = datetime.combine(appointment_date, time(start_hour, 0))
            end_time = datetime.combine(appointment_date, time(end_hour, 0))

            while current_time < end_time:
                slot_time = current_time.time()

                # Check if slot is available
                existing_appointment = Appointment.query.filter_by(
                    appointment_date=appointment_date,
                    appointment_time=slot_time
                )

                if barber_id:
                    existing_appointment = existing_appointment.filter_by(barber_id=barber_id)

                if not existing_appointment.first():
                    available_slots.append(slot_time.strftime('%H:%M'))

                current_time += timedelta(minutes=30)

            return jsonify({'available_slots': available_slots})

        except Exception as e:
            return jsonify({'error': f'حدث خطأ: {str(e)}'}), 400

    # Profile and Settings API
    @app.route('/api/profile', methods=['GET', 'PUT'])
    @login_required
    def api_profile():
        if request.method == 'GET':
            return jsonify({
                'id': current_user.id,
                'username': current_user.username,
                'email': current_user.email,
                'full_name': current_user.full_name,
                'full_name_ar': current_user.full_name_ar,
                'phone': current_user.phone,
                'role': current_user.role,
                'last_login': current_user.last_login.strftime('%Y-%m-%d %H:%M:%S') if current_user.last_login else None,
                'created_at': current_user.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })

        elif request.method == 'PUT':
            data = request.get_json()

            try:
                if 'full_name' in data:
                    current_user.full_name = data['full_name']

                if 'full_name_ar' in data:
                    current_user.full_name_ar = data['full_name_ar']

                if 'email' in data:
                    current_user.email = data['email']

                if 'phone' in data:
                    current_user.phone = data['phone']

                db.session.commit()

                return jsonify({
                    'success': True,
                    'message': 'تم تحديث الملف الشخصي بنجاح'
                })

            except Exception as e:
                db.session.rollback()
                return jsonify({
                    'success': False,
                    'message': f'حدث خطأ في التحديث: {str(e)}'
                }), 400

    # Additional API endpoints for appointments
    @app.route('/api/customers')
    @login_required
    def api_customers():
        customers = Customer.query.all()
        return jsonify({
            'customers': [{
                'id': customer.id,
                'name': customer.name,
                'phone': customer.phone,
                'email': customer.email
            } for customer in customers]
        })

    @app.route('/api/services')
    @login_required
    def api_services():
        services = Service.query.filter_by(is_active=True).all()
        return jsonify({
            'services': [{
                'id': service.id,
                'name': service.name,
                'price': float(service.price),
                'duration_minutes': service.duration_minutes,
                'category': service.category
            } for service in services]
        })

    @app.route('/api/change-password', methods=['POST'])
    @login_required
    def api_change_password():
        from werkzeug.security import check_password_hash
        data = request.get_json()

        current_password = data.get('current_password')
        new_password = data.get('new_password')

        if not current_password or not new_password:
            return jsonify({
                'success': False,
                'message': 'كلمة المرور الحالية والجديدة مطلوبتان'
            }), 400

        # Check current password
        if not check_password_hash(current_user.password_hash, current_password):
            return jsonify({
                'success': False,
                'message': 'كلمة المرور الحالية غير صحيحة'
            }), 400

        try:
            # Update password
            current_user.password_hash = generate_password_hash(new_password)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم تغيير كلمة المرور بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في تغيير كلمة المرور: {str(e)}'
            }), 400

    # Staff Management API Endpoints
    @app.route('/api/staff/<int:staff_id>/details')
    @login_required
    def api_staff_details(staff_id):
        barber = Barber.query.get_or_404(staff_id)

        # Calculate experience years
        hire_date = barber.hire_date
        experience_years = 0
        if hire_date:
            today = date.today()
            experience_years = today.year - hire_date.year
            if today.month < hire_date.month or (today.month == hire_date.month and today.day < hire_date.day):
                experience_years -= 1

        # Get performance data (placeholder - would come from performance tables)
        total_customers = 150 + (staff_id * 25)  # Placeholder
        total_revenue = 5000 + (staff_id * 1000)  # Placeholder

        return jsonify({
            'staff': {
                'id': barber.id,
                'name': barber.name,
                'specialties': barber.specialties,
                'rating': float(barber.rating) if barber.rating else 0.0,
                'total_customers': total_customers,
                'total_revenue': total_revenue,
                'experience_years': experience_years,
                'hire_date': hire_date.strftime('%Y-%m-%d') if hire_date else None,
                'is_active': barber.is_active
            }
        })

    @app.route('/api/reports/top-performers')
    @login_required
    def api_top_performers():
        barbers = Barber.query.filter_by(is_active=True).all()

        performers = []
        for barber in barbers:
            # Calculate performance score (placeholder algorithm)
            score = float(barber.rating) if barber.rating else 0.0
            score += (barber.total_services or 0) * 0.01  # Bonus for experience

            performers.append({
                'id': barber.id,
                'name': barber.name,
                'specialties': barber.specialties,
                'score': score,
                'rating': float(barber.rating) if barber.rating else 0.0
            })

        # Sort by score descending
        performers.sort(key=lambda x: x['score'], reverse=True)

        return jsonify({
            'performers': performers[:10]  # Top 10
        })

    @app.route('/api/reports/staff-performance-summary')
    @login_required
    def api_staff_performance_summary_new():
        period = request.args.get('period', 'month')
        status = request.args.get('status', 'all')
        specialty = request.args.get('specialty', 'all')

        # Get barbers based on filters
        barbers_query = Barber.query

        if status == 'active':
            barbers_query = barbers_query.filter_by(is_active=True)
        elif status == 'inactive':
            barbers_query = barbers_query.filter_by(is_active=False)

        if specialty != 'all':
            barbers_query = barbers_query.filter(Barber.specialties.contains(specialty))

        barbers = barbers_query.all()

        # Calculate summary
        total_staff = len(barbers)
        active_staff = len([b for b in barbers if b.is_active])

        # Calculate average rating
        ratings = [float(b.rating) for b in barbers if b.rating]
        average_rating = sum(ratings) / len(ratings) if ratings else 0

        # Calculate total revenue (simplified)
        total_revenue = 0
        staff_data = []

        for barber in barbers:
            # Get customers for this barber
            customers = Customer.query.filter_by(preferred_barber_id=barber.id).all()
            customers_served = len(customers)
            repeat_customers = len([c for c in customers if c.visit_count > 1])

            # Calculate revenue
            barber_revenue = sum(c.total_spent for c in customers)
            total_revenue += barber_revenue
            average_revenue = barber_revenue / customers_served if customers_served > 0 else 0

            # Calculate performance metrics
            rating = float(barber.rating) if barber.rating else 0
            punctuality = min(85 + (rating - 3) * 15, 100) if rating > 0 else 85
            average_service_time = max(20, 35 - (rating - 3) * 5) if rating > 0 else 30

            staff_data.append({
                'id': barber.id,
                'name': barber.name,
                'specialties': barber.specialties or 'غير محدد',
                'rating': rating,
                'customers_served': customers_served,
                'repeat_customers': repeat_customers,
                'total_revenue': barber_revenue,
                'average_revenue': average_revenue,
                'punctuality': punctuality,
                'average_service_time': average_service_time,
                'is_active': barber.is_active
            })

        return jsonify({
            'summary': {
                'total_staff': total_staff,
                'active_staff': active_staff,
                'average_rating': average_rating,
                'total_revenue': total_revenue
            },
            'staff': staff_data
        })

    # Customer Management API Endpoints
    @app.route('/api/customers', methods=['POST'])
    @login_required
    def api_create_customer():
        data = request.get_json()

        try:
            # Validate required fields
            if not data.get('name') or not data.get('phone'):
                return jsonify({
                    'success': False,
                    'message': 'الاسم ورقم الهاتف مطلوبان'
                }), 400

            # Check if phone number already exists
            existing_customer = Customer.query.filter_by(phone=data['phone']).first()
            if existing_customer:
                return jsonify({
                    'success': False,
                    'message': 'رقم الهاتف مسجل مسبقاً'
                }), 400

            # Parse birthdate if provided
            date_of_birth = None
            if data.get('birthdate'):
                try:
                    date_of_birth = datetime.strptime(data['birthdate'], '%Y-%m-%d').date()
                except ValueError:
                    return jsonify({
                        'success': False,
                        'message': 'تنسيق تاريخ الميلاد غير صحيح'
                    }), 400

            # Create new customer
            customer = Customer(
                name=data['name'],
                phone=data['phone'],
                email=data.get('email') if data.get('email') else None,
                date_of_birth=date_of_birth,
                address=data.get('address') if data.get('address') else None,
                preferred_barber_id=data.get('preferred_barber_id') if data.get('preferred_barber_id') else None,
                barber_loyalty_points=data.get('barber_loyalty_points', 0),
                notes=data.get('notes') if data.get('notes') else None,
                is_vip=data.get('is_vip', False),
                allow_marketing=data.get('allow_marketing', True)
            )

            db.session.add(customer)
            db.session.commit()

            return jsonify({
                'success': True,
                'customer_id': customer.id,
                'message': 'تم إضافة العميل بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في إضافة العميل: {str(e)}'
            }), 400

    @app.route('/api/customers/<int:customer_id>')
    @login_required
    def api_get_customer(customer_id):
        customer = Customer.query.get_or_404(customer_id)

        return jsonify({
            'id': customer.id,
            'name': customer.name,
            'phone': customer.phone,
            'email': customer.email,
            'birthdate': customer.birthdate.strftime('%Y-%m-%d') if customer.birthdate else None,
            'address': customer.address,
            'visit_count': customer.visit_count,
            'total_spent': float(customer.total_spent),
            'last_visit': customer.last_visit.strftime('%Y-%m-%d') if customer.last_visit else None,
            'preferred_barber_id': customer.preferred_barber_id,
            'preferred_barber_name': customer.preferred_barber.name if customer.preferred_barber else None,
            'barber_loyalty_points': customer.barber_loyalty_points,
            'is_vip': customer.is_vip,
            'allow_marketing': customer.allow_marketing,
            'notes': customer.notes,
            'created_at': customer.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    @app.route('/api/customers/<int:customer_id>/preferences')
    @login_required
    def api_get_customer_preferences(customer_id):
        customer = Customer.query.get_or_404(customer_id)

        return jsonify({
            'preferred_barber_id': customer.preferred_barber_id,
            'barber_loyalty_points': customer.barber_loyalty_points,
            'notes': customer.notes
        })

    # Chair Management API Endpoints
    @app.route('/api/chairs', methods=['POST'])
    @login_required
    def api_create_chair():
        data = request.get_json()

        try:
            # Check if chair number already exists
            existing_chair = Chair.query.filter_by(chair_number=data['chair_number']).first()
            if existing_chair:
                return jsonify({
                    'success': False,
                    'message': 'رقم الكرسي موجود مسبقاً'
                }), 400

            # Create new chair
            chair = Chair(
                chair_number=data['chair_number'],
                name=data.get('name') or f"كرسي رقم {data['chair_number']}",
                barber_id=data.get('barber_id'),
                notes=data.get('notes'),
                status=ChairStatus.AVAILABLE
            )

            db.session.add(chair)
            db.session.commit()

            return jsonify({
                'success': True,
                'chair_id': chair.id,
                'message': 'تم إضافة الكرسي بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في إضافة الكرسي: {str(e)}'
            }), 400

    @app.route('/api/chairs/<int:chair_id>/status', methods=['POST'])
    @login_required
    def api_update_chair_status(chair_id):
        chair = Chair.query.get_or_404(chair_id)
        data = request.get_json()

        try:
            # Update chair status
            status_map = {
                'available': ChairStatus.AVAILABLE,
                'occupied': ChairStatus.OCCUPIED,
                'maintenance': ChairStatus.MAINTENANCE,
                'vacation': ChairStatus.VACATION
            }

            new_status = data.get('status')
            if new_status in status_map:
                chair.status = status_map[new_status]

                # Add notes if provided
                if data.get('notes'):
                    chair.notes = data['notes']

                # Update maintenance date if status is maintenance
                if new_status == 'maintenance':
                    chair.last_maintenance = datetime.utcnow()

                db.session.commit()

                return jsonify({
                    'success': True,
                    'message': 'تم تحديث حالة الكرسي بنجاح'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'حالة الكرسي غير صحيحة'
                }), 400

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في تحديث حالة الكرسي: {str(e)}'
            }), 400

    @app.route('/api/chairs/<int:chair_id>/complete-maintenance', methods=['POST'])
    @login_required
    def api_complete_maintenance(chair_id):
        chair = Chair.query.get_or_404(chair_id)

        try:
            chair.status = ChairStatus.AVAILABLE
            chair.last_maintenance = datetime.utcnow()
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم إنهاء الصيانة وتفعيل الكرسي'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في إنهاء الصيانة: {str(e)}'
            }), 400

    @app.route('/api/chairs/<int:chair_id>/activate', methods=['POST'])
    @login_required
    def api_activate_chair(chair_id):
        chair = Chair.query.get_or_404(chair_id)

        try:
            chair.status = ChairStatus.AVAILABLE
            chair.is_active = True
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم تفعيل الكرسي بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في تفعيل الكرسي: {str(e)}'
            }), 400

    @app.route('/api/chairs/<int:chair_id>', methods=['DELETE'])
    @login_required
    def api_delete_chair(chair_id):
        chair = Chair.query.get_or_404(chair_id)

        try:
            # Check if chair has active appointments
            active_appointments = Appointment.query.filter_by(
                chair_id=chair_id,
                status=AppointmentStatus.SCHEDULED
            ).count()

            if active_appointments > 0:
                return jsonify({
                    'success': False,
                    'message': 'لا يمكن حذف الكرسي لوجود مواعيد نشطة'
                }), 400

            db.session.delete(chair)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم حذف الكرسي بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في حذف الكرسي: {str(e)}'
            }), 400

    # Staff Management API Endpoints
    @app.route('/api/staff', methods=['POST'])
    @login_required
    def api_create_staff():
        data = request.get_json()

        try:
            # Validate required fields
            if not data.get('name') or not data.get('phone') or not data.get('hire_date'):
                return jsonify({
                    'success': False,
                    'message': 'الاسم ورقم الهاتف وتاريخ التوظيف مطلوبان'
                }), 400

            # Check if phone number already exists
            existing_staff = Barber.query.filter_by(phone=data['phone']).first()
            if existing_staff:
                return jsonify({
                    'success': False,
                    'message': 'رقم الهاتف مسجل مسبقاً'
                }), 400

            # Parse hire date
            try:
                hire_date = datetime.strptime(data['hire_date'], '%Y-%m-%d').date()
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': 'تنسيق تاريخ التوظيف غير صحيح'
                }), 400

            # Parse working hours
            working_hours_start = None
            working_hours_end = None

            if data.get('working_hours_start'):
                try:
                    working_hours_start = datetime.strptime(data['working_hours_start'], '%H:%M').time()
                except ValueError:
                    working_hours_start = time(8, 0)  # Default start time

            if data.get('working_hours_end'):
                try:
                    working_hours_end = datetime.strptime(data['working_hours_end'], '%H:%M').time()
                except ValueError:
                    working_hours_end = time(20, 0)  # Default end time

            # Create new staff member (barber)
            staff = Barber(
                name=data['name'],
                name_ar=data.get('name_ar'),
                phone=data['phone'],
                email=data.get('email'),
                hire_date=hire_date,
                salary=data.get('salary', 0.0),
                commission_rate=data.get('commission_rate', 0.0),
                rating=data.get('rating', 4.0),
                specialties=data.get('specialties'),
                working_hours_start=working_hours_start,
                working_hours_end=working_hours_end,
                working_days=data.get('working_days', '1,2,3,4,5,6'),
                is_active=data.get('is_active', True),
                total_services=0
            )

            db.session.add(staff)
            db.session.commit()

            return jsonify({
                'success': True,
                'staff_id': staff.id,
                'message': 'تم إضافة الموظف بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في إضافة الموظف: {str(e)}'
            }), 400

    @app.route('/api/staff/<int:staff_id>', methods=['PUT'])
    @login_required
    def api_update_staff(staff_id):
        staff = Barber.query.get_or_404(staff_id)
        data = request.get_json()

        try:
            # Update staff information
            if 'name' in data:
                staff.name = data['name']

            if 'name_ar' in data:
                staff.name_ar = data['name_ar']

            if 'phone' in data:
                # Check if phone number already exists for another staff member
                existing_staff = Barber.query.filter(
                    Barber.phone == data['phone'],
                    Barber.id != staff_id
                ).first()
                if existing_staff:
                    return jsonify({
                        'success': False,
                        'message': 'رقم الهاتف مسجل مسبقاً لموظف آخر'
                    }), 400
                staff.phone = data['phone']

            if 'email' in data:
                staff.email = data['email']

            if 'salary' in data:
                staff.salary = data['salary']

            if 'commission_rate' in data:
                staff.commission_rate = data['commission_rate']

            if 'rating' in data:
                staff.rating = data['rating']

            if 'specialties' in data:
                staff.specialties = data['specialties']

            if 'working_hours_start' in data:
                try:
                    staff.working_hours_start = datetime.strptime(data['working_hours_start'], '%H:%M').time()
                except ValueError:
                    pass

            if 'working_hours_end' in data:
                try:
                    staff.working_hours_end = datetime.strptime(data['working_hours_end'], '%H:%M').time()
                except ValueError:
                    pass

            if 'working_days' in data:
                staff.working_days = data['working_days']

            if 'is_active' in data:
                staff.is_active = data['is_active']

            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم تحديث معلومات الموظف بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في تحديث الموظف: {str(e)}'
            }), 400

    @app.route('/api/staff/<int:staff_id>', methods=['DELETE'])
    @login_required
    def api_delete_staff(staff_id):
        staff = Barber.query.get_or_404(staff_id)

        try:
            # Check if staff has active appointments
            active_appointments = Appointment.query.filter_by(
                barber_id=staff_id,
                status=AppointmentStatus.SCHEDULED
            ).count()

            if active_appointments > 0:
                return jsonify({
                    'success': False,
                    'message': 'لا يمكن حذف الموظف لوجود مواعيد نشطة'
                }), 400

            # Instead of deleting, deactivate the staff member
            staff.is_active = False
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'تم إلغاء تفعيل الموظف بنجاح'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'success': False,
                'message': f'حدث خطأ في حذف الموظف: {str(e)}'
            }), 400

    return app

def create_sample_data():
    """Create sample data for testing"""
    if User.query.count() == 0:
        # Create admin user
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='مدير النظام',
            full_name_ar='مدير النظام',
            role=UserRole.MANAGER
        )
        admin.set_password('admin123')
        db.session.add(admin)
        
        # Create sample barber
        barber1 = Barber(
            name='أحمد محمد',
            name_ar='أحمد محمد',
            phone='+974-1234-5678',
            hire_date=date.today(),
            salary=3000.0,
            commission_rate=10.0
        )
        db.session.add(barber1)
        
        # Create sample chairs
        for i in range(1, 6):
            chair = Chair(
                chair_number=i,
                name=f'كرسي رقم {i}',
                barber_id=1 if i <= 2 else None
            )
            db.session.add(chair)
        
        # Create sample services
        services_data = [
            ('حلاقة عادية', 'Regular Haircut', 25.0, 30),
            ('حلاقة وتهذيب ذقن', 'Haircut & Beard Trim', 35.0, 45),
            ('تنظيف بشرة', 'Facial Treatment', 50.0, 60),
            ('صبغة شعر', 'Hair Coloring', 80.0, 90),
            ('حلاقة ذقن فقط', 'Beard Trim Only', 15.0, 20)
        ]
        
        for name_ar, name_en, price, duration in services_data:
            service = Service(
                name=name_en,
                name_ar=name_ar,
                price=price,
                duration_minutes=duration,
                category='حلاقة'
            )
            db.session.add(service)
        
        db.session.commit()

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
